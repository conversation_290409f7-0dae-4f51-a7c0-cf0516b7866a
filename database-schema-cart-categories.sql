-- Guild Cart Configuration Table
-- Stores cart mode preferences for each guild
CREATE TABLE asa.guild_cart_settings (
    setting_id SERIAL PRIMARY KEY,
    guild_id VARCHAR(20) NOT NULL UNIQUE,
    cart_mode VARCHAR(20) NOT NULL DEFAULT 'single' CHECK (cart_mode IN ('single', 'threads')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Category Role Permissions Table
-- Stores which roles can access which categories per guild
CREATE TABLE asa.guild_category_permissions (
    permission_id SERIAL PRIMARY KEY,
    guild_id VARCHAR(20) NOT NULL,
    category asa.item_category NOT NULL,
    role_id VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(guild_id, category, role_id)
);

-- Create indexes for better performance
CREATE INDEX idx_guild_cart_settings_guild_id ON asa.guild_cart_settings(guild_id);
CREATE INDEX idx_guild_category_permissions_guild_id ON asa.guild_category_permissions(guild_id);
CREATE INDEX idx_guild_category_permissions_category ON asa.guild_category_permissions(guild_id, category);

-- Add comments for documentation
COMMENT ON TABLE asa.guild_cart_settings IS 'Stores cart mode configuration for guilds - single cart vs category threads';
COMMENT ON COLUMN asa.guild_cart_settings.guild_id IS 'Discord guild (server) ID';
COMMENT ON COLUMN asa.guild_cart_settings.cart_mode IS 'Cart mode: single (all items in one cart) or threads (items split by category in threads)';

COMMENT ON TABLE asa.guild_category_permissions IS 'Stores category-specific role permissions for thread-based cart system';
COMMENT ON COLUMN asa.guild_category_permissions.guild_id IS 'Discord guild (server) ID';
COMMENT ON COLUMN asa.guild_category_permissions.category IS 'Item category that the role has access to';
COMMENT ON COLUMN asa.guild_category_permissions.role_id IS 'Discord role ID that gets access to the category';
