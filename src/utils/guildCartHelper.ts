import pool from './dbConfig';

export type CartMode = 'single' | 'threads';
export type ItemCategory = 'item' | 'dino' | 'egg' | 'blueprint' | 'boss_fight' | 'base_spot' | 'xp_party' | 'other' | 'bundle';

/**
 * Helper class for managing guild cart settings and category permissions
 */
export class GuildCartHelper {
  /**
   * Get the cart mode for a guild (default: 'single')
   */
  static async getGuildCartMode(guildId: string): Promise<CartMode> {
    try {
      const query = `
        SELECT cart_mode
        FROM asa.guild_cart_settings
        WHERE guild_id = $1
      `;

      const result = await pool.query(query, [guildId]);
      return result.rows.length > 0 ? result.rows[0].cart_mode : 'single';
    } catch (error) {
      console.error('Error getting guild cart mode:', error);
      return 'single';
    }
  }

  /**
   * Set the cart mode for a guild
   */
  static async setGuildCartMode(guildId: string, cartMode: CartMode): Promise<boolean> {
    try {
      const query = `
        INSERT INTO asa.guild_cart_settings (guild_id, cart_mode, updated_at)
        VALUES ($1, $2, NOW())
        ON CONFLICT (guild_id)
        DO UPDATE SET cart_mode = $2, updated_at = NOW()
      `;

      await pool.query(query, [guildId, cartMode]);
      return true;
    } catch (error) {
      console.error('Error setting guild cart mode:', error);
      return false;
    }
  }

  /**
   * Get all roles that have access to a specific category in a guild
   */
  static async getCategoryRoles(guildId: string, category: ItemCategory): Promise<string[]> {
    try {
      const query = `
        SELECT role_id
        FROM asa.guild_category_permissions
        WHERE guild_id = $1 AND category = $2
      `;

      const result = await pool.query(query, [guildId, category]);
      return result.rows.map(row => row.role_id);
    } catch (error) {
      console.error('Error getting category roles:', error);
      return [];
    }
  }

  /**
   * Get all category permissions for a guild
   */
  static async getAllCategoryPermissions(guildId: string): Promise<Record<ItemCategory, string[]>> {
    try {
      const query = `
        SELECT category, role_id
        FROM asa.guild_category_permissions
        WHERE guild_id = $1
        ORDER BY category, role_id
      `;

      const result = await pool.query(query, [guildId]);
      
      const permissions: Record<ItemCategory, string[]> = {
        item: [],
        dino: [],
        egg: [],
        blueprint: [],
        boss_fight: [],
        base_spot: [],
        xp_party: [],
        other: [],
        bundle: []
      };

      for (const row of result.rows) {
        if (permissions[row.category as ItemCategory]) {
          permissions[row.category as ItemCategory].push(row.role_id);
        }
      }

      return permissions;
    } catch (error) {
      console.error('Error getting all category permissions:', error);
      return {
        item: [],
        dino: [],
        egg: [],
        blueprint: [],
        boss_fight: [],
        base_spot: [],
        xp_party: [],
        other: [],
        bundle: []
      };
    }
  }

  /**
   * Add a role to a category permission
   */
  static async addCategoryRole(guildId: string, category: ItemCategory, roleId: string): Promise<boolean> {
    try {
      const query = `
        INSERT INTO asa.guild_category_permissions (guild_id, category, role_id)
        VALUES ($1, $2, $3)
        ON CONFLICT (guild_id, category, role_id) DO NOTHING
      `;

      const result = await pool.query(query, [guildId, category, roleId]);
      return result.rowCount !== null && result.rowCount > 0;
    } catch (error) {
      console.error('Error adding category role:', error);
      return false;
    }
  }

  /**
   * Remove a role from a category permission
   */
  static async removeCategoryRole(guildId: string, category: ItemCategory, roleId: string): Promise<boolean> {
    try {
      const query = `
        DELETE FROM asa.guild_category_permissions
        WHERE guild_id = $1 AND category = $2 AND role_id = $3
      `;

      const result = await pool.query(query, [guildId, category, roleId]);
      return result.rowCount !== null && result.rowCount > 0;
    } catch (error) {
      console.error('Error removing category role:', error);
      return false;
    }
  }

  /**
   * Clear all roles for a specific category in a guild
   */
  static async clearCategoryRoles(guildId: string, category: ItemCategory): Promise<boolean> {
    try {
      const query = `
        DELETE FROM asa.guild_category_permissions
        WHERE guild_id = $1 AND category = $2
      `;

      await pool.query(query, [guildId, category]);
      return true;
    } catch (error) {
      console.error('Error clearing category roles:', error);
      return false;
    }
  }

  /**
   * Clear all category permissions for a guild
   */
  static async clearAllCategoryPermissions(guildId: string): Promise<boolean> {
    try {
      const query = `
        DELETE FROM asa.guild_category_permissions
        WHERE guild_id = $1
      `;

      await pool.query(query, [guildId]);
      return true;
    } catch (error) {
      console.error('Error clearing all category permissions:', error);
      return false;
    }
  }

  /**
   * Check if a user has access to a specific category based on their roles
   */
  static async userHasCategoryAccess(
    guildId: string, 
    category: ItemCategory, 
    userRoleIds: string[]
  ): Promise<boolean> {
    try {
      const categoryRoles = await this.getCategoryRoles(guildId, category);
      
      // If no roles are configured for this category, allow access (backward compatibility)
      if (categoryRoles.length === 0) {
        return true;
      }

      // Check if user has any of the required roles
      return userRoleIds.some(roleId => categoryRoles.includes(roleId));
    } catch (error) {
      console.error('Error checking user category access:', error);
      return true; // Default to allowing access on error
    }
  }

  /**
   * Get categories that a user has access to based on their roles
   */
  static async getUserAccessibleCategories(
    guildId: string, 
    userRoleIds: string[]
  ): Promise<ItemCategory[]> {
    try {
      const allPermissions = await this.getAllCategoryPermissions(guildId);
      const accessibleCategories: ItemCategory[] = [];

      for (const [category, roleIds] of Object.entries(allPermissions)) {
        // If no roles configured for category, user has access
        if (roleIds.length === 0) {
          accessibleCategories.push(category as ItemCategory);
        } else if (userRoleIds.some(roleId => roleIds.includes(roleId))) {
          accessibleCategories.push(category as ItemCategory);
        }
      }

      return accessibleCategories;
    } catch (error) {
      console.error('Error getting user accessible categories:', error);
      // Return all categories on error for backward compatibility
      return ['item', 'dino', 'egg', 'blueprint', 'boss_fight', 'base_spot', 'xp_party', 'other', 'bundle'];
    }
  }
}
