import { Collection } from 'discord.js';
import { ItemCategory } from './guildCartHelper';

// Interface for cart items
export interface CartItem {
  listingId: number;
  listingItemId?: number;
  title: string;
  quantity: number;
  price?: string;
  paymentMethod?: string;
  paymentId?: number;
  listingType?: 'buy' | 'sell'; // Track whether this is a buying or selling listing
  category?: ItemCategory; // Category for thread-based cart system
}

// Interface for user carts
export interface UserCart {
  userId: string;
  guildId: string;
  items: CartItem[];
  createdAt: Date;
  updatedAt: Date;
}

class CartManager {
  private carts: Collection<string, UserCart>;

  constructor() {
    this.carts = new Collection<string, UserCart>();
  }

  /**
   * Get a cart key from user and guild IDs
   */
  private getCartKey(userId: string, guildId: string): string {
    return `${userId}-${guildId}`;
  }

  /**
   * Get a user's cart
   */
  getCart(userId: string, guildId: string): UserCart | undefined {
    const key = this.getCartKey(userId, guildId);
    return this.carts.get(key);
  }

  /**
   * Create a new cart for a user
   */
  createCart(userId: string, guildId: string): UserCart {
    const key = this.getCartKey(userId, guildId);
    const now = new Date();

    const cart: UserCart = {
      userId,
      guildId,
      items: [],
      createdAt: now,
      updatedAt: now
    };

    this.carts.set(key, cart);
    return cart;
  }

  /**
   * Get or create a cart for a user
   */
  getOrCreateCart(userId: string, guildId: string): UserCart {
    const existingCart = this.getCart(userId, guildId);
    if (existingCart) return existingCart;
    return this.createCart(userId, guildId);
  }

  /**
   * Add an item to a user's cart
   */
  addItem(userId: string, guildId: string, item: CartItem): CartItem {
    const cart = this.getOrCreateCart(userId, guildId);

    // Check if the item already exists in the cart
    // Items are considered the same if they have the same listingId, listingItemId, paymentId, paymentMethod, AND listingType
    const existingItemIndex = cart.items.findIndex(i =>
      i.listingId === item.listingId &&
      i.listingItemId === item.listingItemId &&
      i.paymentId === item.paymentId &&
      i.paymentMethod === item.paymentMethod &&
      i.listingType === item.listingType
    );

    if (existingItemIndex !== -1) {
      // Update the existing item
      cart.items[existingItemIndex].quantity += item.quantity;
      cart.updatedAt = new Date();
      return cart.items[existingItemIndex];
    } else {
      // Add as a new item
      cart.items.push(item);
      cart.updatedAt = new Date();
      return item;
    }
  }

  /**
   * Remove an item from a user's cart
   */
  removeItem(userId: string, guildId: string, index: number): boolean {
    const cart = this.getCart(userId, guildId);
    if (!cart || !cart.items[index]) return false;

    cart.items.splice(index, 1);
    cart.updatedAt = new Date();
    return true;
  }

  /**
   * Update the quantity of an item in a user's cart
   */
  updateItemQuantity(userId: string, guildId: string, index: number, quantity: number): boolean {
    const cart = this.getCart(userId, guildId);
    if (!cart || !cart.items[index]) return false;

    cart.items[index].quantity = quantity;
    cart.updatedAt = new Date();
    return true;
  }

  /**
   * Clear a user's cart
   */
  clearCart(userId: string, guildId: string): boolean {
    const cart = this.getCart(userId, guildId);
    if (!cart) return false;

    cart.items = [];
    cart.updatedAt = new Date();
    return true;
  }

  /**
   * Remove a specific item from the cart by matching properties
   */
  removeItemByProperties(
    userId: string,
    guildId: string,
    title: string,
    quantity: number,
    paymentMethod: string,
    listingType?: 'buy' | 'sell'
  ): boolean {
    const cart = this.getCart(userId, guildId);

    if (!cart) return false;

    // Find the item index by matching properties
    const itemIndex = cart.items.findIndex(item =>
      item.title === title &&
      item.quantity === quantity &&
      item.paymentMethod === paymentMethod &&
      item.listingType === listingType
    );

    if (itemIndex !== -1) {
      cart.items.splice(itemIndex, 1);
      cart.updatedAt = new Date();
      return true;
    }

    return false;
  }

  /**
   * Delete a user's cart
   */
  deleteCart(userId: string, guildId: string): boolean {
    const key = this.getCartKey(userId, guildId);
    return this.carts.delete(key);
  }

  /**
   * Get items from a user's cart by category
   */
  getItemsByCategory(userId: string, guildId: string, category: ItemCategory): CartItem[] {
    const cart = this.getCart(userId, guildId);
    if (!cart) return [];

    return cart.items.filter(item => item.category === category);
  }

  /**
   * Get all items grouped by category
   */
  getItemsGroupedByCategory(userId: string, guildId: string): Record<ItemCategory, CartItem[]> {
    const cart = this.getCart(userId, guildId);
    const grouped: Record<ItemCategory, CartItem[]> = {
      item: [],
      dino: [],
      egg: [],
      blueprint: [],
      boss_fight: [],
      base_spot: [],
      xp_party: [],
      other: [],
      bundle: []
    };

    if (!cart) return grouped;

    for (const item of cart.items) {
      const category = item.category || 'other';
      if (grouped[category]) {
        grouped[category].push(item);
      }
    }

    return grouped;
  }

  /**
   * Get categories that have items in the cart
   */
  getCategoriesWithItems(userId: string, guildId: string): ItemCategory[] {
    const cart = this.getCart(userId, guildId);
    if (!cart) return [];

    const categories = new Set<ItemCategory>();
    for (const item of cart.items) {
      categories.add(item.category || 'other');
    }

    return Array.from(categories);
  }

  /**
   * Calculate the total price of a user's cart
   * (This would need to be implemented based on how prices are stored)
   */
  calculateTotal(userId: string, guildId: string): string {
    const cart = this.getCart(userId, guildId);
    if (!cart || cart.items.length === 0) return "0";

    // This is a simplified example - you would need to implement the actual pricing logic
    // based on how your prices are stored (whether as numbers or as item quantities)
    const itemCounts: { [key: string]: number } = {};

    for (const item of cart.items) {
      if (item.paymentMethod) {
        itemCounts[item.paymentMethod] = (itemCounts[item.paymentMethod] || 0) + item.quantity;
      }
    }

    // Format the total as a string
    return Object.entries(itemCounts)
      .map(([method, count]) => `${method}: ${count}`)
      .join(', ');
  }
}

// Create and export a singleton instance
export const cartManager = new CartManager();