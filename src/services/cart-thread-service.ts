import {
  TextChannel,
  ThreadChannel,
  ChannelType,
  GuildMember
} from 'discord.js';
import { GuildCartHelper, ItemCategory } from '../utils/guildCartHelper';
import { GuildRoleHelper } from '../utils/guildRoleHelper';

const CATEGORY_EMOJIS: Record<ItemCategory, string> = {
  item: '🎒',
  dino: '🦕',
  egg: '🥚',
  blueprint: '📋',
  boss_fight: '⚔️',
  base_spot: '🏠',
  xp_party: '🎉',
  other: '📦',
  bundle: '🎁'
};

const CATEGORY_NAMES: Record<ItemCategory, string> = {
  item: 'Items',
  dino: 'Dinosaurs',
  egg: 'Eggs',
  blueprint: 'Blueprints',
  boss_fight: 'Boss Fights',
  base_spot: 'Base Spots',
  xp_party: 'XP Parties',
  other: 'Other',
  bundle: 'Bundles'
};

export class CartThreadService {

  /**
   * Create a single category thread with appropriate permissions
   */
  private static async createCategoryThread(
    cartChannel: TextChannel,
    category: ItemCategory,
    userId: string,
    _userRoleIds: string[],
    cartAccessRoles: string[],
    adminAccessRoles: string[]
  ): Promise<ThreadChannel | null> {
    try {
      const emoji = CATEGORY_EMOJIS[category];
      const name = CATEGORY_NAMES[category];
      const threadName = `${emoji} ${name}`;

      // Create the thread
      const thread = await cartChannel.threads.create({
        name: threadName,
        type: ChannelType.PrivateThread,
        reason: `Category thread for ${name} in cart channel`
      });

      // Add the cart owner to the thread
      await thread.members.add(userId);

      // Add cart access roles to the thread
      for (const roleId of cartAccessRoles) {
        const role = cartChannel.guild?.roles.cache.get(roleId);
        if (role) {
          // Get all members with this role and add them to the thread
          const membersWithRole = cartChannel.guild?.members.cache.filter(member => 
            member.roles.cache.has(roleId)
          );
          
          if (membersWithRole) {
            for (const [, member] of membersWithRole) {
              try {
                await thread.members.add(member.id);
              } catch (error) {
                console.error(`Failed to add member ${member.id} to thread:`, error);
              }
            }
          }
        }
      }

      // Add admin access roles to the thread
      for (const roleId of adminAccessRoles) {
        const role = cartChannel.guild?.roles.cache.get(roleId);
        if (role) {
          const membersWithRole = cartChannel.guild?.members.cache.filter(member => 
            member.roles.cache.has(roleId)
          );
          
          if (membersWithRole) {
            for (const [, member] of membersWithRole) {
              try {
                await thread.members.add(member.id);
              } catch (error) {
                console.error(`Failed to add admin member ${member.id} to thread:`, error);
              }
            }
          }
        }
      }

      // Get category-specific roles and add them to the thread
      const categoryRoles = await GuildCartHelper.getCategoryRoles(cartChannel.guild!.id, category);
      for (const roleId of categoryRoles) {
        const role = cartChannel.guild?.roles.cache.get(roleId);
        if (role) {
          const membersWithRole = cartChannel.guild?.members.cache.filter(member => 
            member.roles.cache.has(roleId)
          );
          
          if (membersWithRole) {
            for (const [, member] of membersWithRole) {
              try {
                await thread.members.add(member.id);
              } catch (error) {
                console.error(`Failed to add category member ${member.id} to thread:`, error);
              }
            }
          }
        }
      }

      // Send a welcome message to the thread
      await thread.send({
        content: `${emoji} **${name} Thread**\n\nThis thread is for ${name.toLowerCase()} related items. Add your ${name.toLowerCase()} to this thread!`
      });

      return thread;
    } catch (error) {
      console.error(`Error creating thread for category ${category}:`, error);
      return null;
    }
  }

  /**
   * Get or create a specific category thread in a cart channel
   */
  static async getOrCreateCategoryThread(
    cartChannel: TextChannel,
    category: ItemCategory,
    userId: string,
    userMember: GuildMember
  ): Promise<ThreadChannel | null> {
    try {
      const emoji = CATEGORY_EMOJIS[category];
      const name = CATEGORY_NAMES[category];
      const threadName = `${emoji} ${name}`;

      // Look for existing thread
      const existingThread = cartChannel.threads.cache.find(thread => 
        thread.name === threadName && !thread.archived
      );

      if (existingThread) {
        // Ensure user is added to the thread
        if (!existingThread.members.cache.has(userId)) {
          await existingThread.members.add(userId);
        }
        return existingThread;
      }

      // Create new thread if it doesn't exist
      const userRoleIds = userMember.roles.cache.map(role => role.id);
      const cartAccessRoles = await GuildRoleHelper.getGuildRoles(cartChannel.guild!.id, 'cart_access');
      const adminAccessRoles = await GuildRoleHelper.getGuildRoles(cartChannel.guild!.id, 'admin_access');

      return await this.createCategoryThread(
        cartChannel,
        category,
        userId,
        userRoleIds,
        cartAccessRoles,
        adminAccessRoles
      );
    } catch (error) {
      console.error(`Error getting/creating category thread for ${category}:`, error);
      return null;
    }
  }

  /**
   * Check if a user has access to a category thread
   */
  static async userHasThreadAccess(
    guildId: string,
    category: ItemCategory,
    userRoleIds: string[]
  ): Promise<boolean> {
    return await GuildCartHelper.userHasCategoryAccess(guildId, category, userRoleIds);
  }

  /**
   * Get the appropriate thread for adding an item based on its category
   */
  static async getThreadForItem(
    cartChannel: TextChannel,
    category: ItemCategory,
    userId: string,
    userMember: GuildMember
  ): Promise<ThreadChannel | TextChannel> {
    // Check if guild is using thread mode
    const cartMode = await GuildCartHelper.getGuildCartMode(cartChannel.guild!.id);
    
    if (cartMode !== 'threads') {
      // Return main channel for single cart mode
      return cartChannel;
    }

    // Check if user has access to this category
    const userRoleIds = userMember.roles.cache.map(role => role.id);
    const hasAccess = await this.userHasThreadAccess(cartChannel.guild!.id, category, userRoleIds);
    
    if (!hasAccess) {
      // User doesn't have access to this category, return main channel
      return cartChannel;
    }

    // Get or create the category thread
    const thread = await this.getOrCreateCategoryThread(cartChannel, category, userId, userMember);
    
    // Return thread if successful, otherwise fall back to main channel
    return thread || cartChannel;
  }
}
