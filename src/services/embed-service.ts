import { EmbedBuilder, AttachmentBuilder } from "discord.js";
import { cartManager } from "../utils/cartManager";
import { ArkDbHelper } from "../utils/arkDbHelper";
import { AvailabilityService } from "./availability-service";
import { ImageService } from "./image-service";
import { EmojiService } from "./emoji-service";


import { NumberFormatter } from "../utils/numberFormatter";

export class EmbedService {
    /**
     * Create a cart embed for displaying cart contents
     */
    static createCartEmbed(userId: string, guildId: string, username: string): EmbedBuilder {
        const cart = cartManager.getCart(userId, guildId);

        const embed = new EmbedBuilder()
            .setTitle('🛒 Your Shopping Cart')
            .setColor('#0099ff')
            .setDescription(`Here are the items in your cart, ${username}:`)
            .setTimestamp();

        if (cart && cart.items.length > 0) {
            // Add each item to the embed
            cart.items.forEach((item, index) => {
                embed.addFields({
                    name: `${index + 1}. ${item.title}`,
                    value: `Quantity: ${NumberFormatter.formatWithCommas(item.quantity)}${item.paymentMethod ? `\nPayment: ${item.paymentMethod}` : ''}`,
                    inline: false
                });
            });

            // Add the total if applicable
            const total = cartManager.calculateTotal(userId, guildId);
            if (total) {
                embed.addFields({
                    name: 'Total',
                    value: total,
                    inline: false
                });
            }
        } else {
            embed.setDescription('Your cart is empty.');
        }

        return embed;
    }

    /**
     * Create a trade ticket embed
     */
    static createTradeTicketEmbed(userId: string, guildId: string, userMention: string): EmbedBuilder {
        const cart = cartManager.getCart(userId, guildId);

        const embed = new EmbedBuilder()
            .setTitle('🛍️ New Trade Ticket')
            .setColor('#00ff00')
            .setDescription(`Trade ticket created by ${userMention}.`)
            .setTimestamp();

        if (cart && cart.items.length > 0) {
            embed.addFields(
                {
                    name: 'Items',
                    value: cart.items.map(item => `${item.title} x${NumberFormatter.formatWithCommas(item.quantity)}`).join('\n')
                },
                {
                    name: 'Total',
                    value: cartManager.calculateTotal(userId, guildId) || 'N/A'
                }
            );
        }

        return embed;
    }



    /**
     * Create a detailed listing embed with availability thumbnail and product image
     */
    static async createListingEmbed(listing: any): Promise<{ embed: EmbedBuilder; attachment?: AttachmentBuilder }> {
        // Get availability configuration
        const availabilityConfig = AvailabilityService.getAvailabilityConfig(listing.availability);

        // Get payment options for this listing
        const paymentOptions = await ArkDbHelper.getPaymentOptions(listing.listing_id);
        let price = `${EmojiService.getCurrencyEmoji('contact')} Contact seller for price`;

        if (paymentOptions.length > 0) {
            // Format all payment options
            const formattedPrices = paymentOptions.map(payment => {
                if (payment.item_id) {
                    const itemName = payment.item_name || 'Item';
                    // Use icon_name from database if available, otherwise fall back to item name
                    const iconIdentifier = payment.icon_name || itemName;
                    const emoji = EmojiService.getItemEmojiByIconName(iconIdentifier);
                    const formattedQuantity = NumberFormatter.formatWithCommas(payment.quantity || 1);
                    return `${emoji} ${itemName} x${formattedQuantity}`;
                } else if (payment.custom_price) {
                    const emoji = EmojiService.getCurrencyEmoji(payment.custom_price);
                    return `${emoji} ${payment.custom_price}`;
                } else if (payment.contact_for_price) {
                    return `${EmojiService.getCurrencyEmoji('contact')} Contact seller for price`;
                }
                return null;
            }).filter(Boolean); // Remove null values

            // Join all formatted prices with newlines if multiple, or use single price
            if (formattedPrices.length > 0) {
                price = formattedPrices.join('\n');
            }
        }

        // New embeds: Description shown by default, Type/Category/Cluster hidden by default
        // Text toggles are applied per-message after creation

        const embed = new EmbedBuilder()
            .setTitle(`${listing.title || `Listing #${listing.listing_id}`}`)
            .setColor(availabilityConfig.color);

        // Set description (shown by default in new embeds)
        embed.setDescription(listing.description || 'No description provided');

        // Note: Type, Category, and Cluster fields are not added here since they're hidden by default
        // They will be added only when explicitly enabled via toggle buttons

        // Always add price field on its own row (inline: false)
        embed.addFields({ name: 'Price', value: price, inline: false });

        // Set main image priority: existing image_urls first, then generated product image
        let productImage = null;

        if (listing.image_urls && listing.image_urls.length > 0) {
            // Use existing image URLs as main image (highest priority)
            embed.setImage(listing.image_urls[0]);
        } else {
            // Generate product preview image as fallback
            productImage = await ImageService.createListingPreviewImage(listing.listing_id);
            if (productImage) {
                embed.setImage(`attachment://${productImage.name}`);
            }
        }

        // Set availability status thumbnail (top-right corner)
        embed.setThumbnail(availabilityConfig.thumbnailUrl);

        // Add default Wikily branding footer (branding toggles are applied per-message later)
        embed.setFooter({
            text: `Powered by\nWikily`,
            iconURL: 'https://r2.wikily.gg/images/brand/wikilyLogo_300.png'
        });

        // Add clickable URL (link toggles are applied per-message later)
        embed.setURL(`https://wikily.gg/ark-survival-ascended/trading/listings/${listing.listing_id}/`);

        return { embed, attachment: productImage || undefined };
    }


}
