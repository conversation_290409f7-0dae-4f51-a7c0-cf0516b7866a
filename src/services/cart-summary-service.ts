import { Embed<PERSON><PERSON><PERSON>, TextChannel, ActionRowBuilder, ButtonBuilder, ButtonStyle, ThreadChannel } from "discord.js";
import { cartManager, CartItem } from "../utils/cartManager";
import { NumberFormatter } from "../utils/numberFormatter";
import { EmojiService } from "./emoji-service";
import { GuildCartHelper } from "../utils/guildCartHelper";

export class CartSummaryService {
    private static readonly SUMMARY_MESSAGE_IDENTIFIER = "🛒 **SHOPPING SUMMARY**";
    private static readonly ITEM_MESSAGE_IDENTIFIER = "🛍️ Item Added to Cart";

    /**
     * Create or update the cart summary at the end of the channel
     * Ensures only one summary exists and it's always at the end
     */
    static async updateCartSummary(
        channel: TextChannel,
        userId: string,
        guildId: string,
        userMention: string
    ): Promise<void> {
        try {
            // Always remove existing summary first to ensure only one exists
            await this.removeSummary(channel);

            // Recreate cart from channel messages to ensure persistence after bot restart
            await this.recreateCartFromChannel(channel, userId, guildId);

            // Create summary embed (even if cart is empty)
            const summaryEmbed = this.createSummaryEmbed(userId, guildId, userMention);

            // Always create admin buttons (permission check happens when pressed)
            const adminButtons = new ActionRowBuilder<ButtonBuilder>()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`ticket_transcript`)
                        .setLabel('Save Transcript')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('📝'),
                    new ButtonBuilder()
                        .setCustomId(`ticket_close`)
                        .setLabel('Close Ticket')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('🔒')
                );

            // Always create new summary at the end
            await channel.send({
                content: this.SUMMARY_MESSAGE_IDENTIFIER,
                embeds: [summaryEmbed],
                components: [adminButtons]
            });
        } catch (error) {
            console.error('Error updating cart summary:', error);
        }
    }

    /**
     * Recreate cart from channel messages (for bot restart persistence)
     */
    static async recreateCartFromChannel(
        channel: TextChannel,
        userId: string,
        guildId: string
    ): Promise<void> {
        try {
            // Clear existing cart first
            cartManager.clearCart(userId, guildId);

            // Fetch all messages from the channel
            const messages = await this.fetchAllMessages(channel);

            // Parse item messages to recreate cart
            for (const message of messages) {
                if (message.embeds.length > 0) {
                    const embed = message.embeds[0];

                    // Check if this is an item embed
                    if (embed.title === this.ITEM_MESSAGE_IDENTIFIER) {
                        const itemData = this.parseItemEmbed(embed);
                        if (itemData) {
                            console.log(`[Cart Recreation] Adding item to cart: "${itemData.title}" x${itemData.quantity} (${itemData.paymentMethod}) [${itemData.listingType}]`);
                            cartManager.addItem(userId, guildId, itemData);
                        } else {
                            console.log(`[Cart Recreation] Failed to parse item embed from message`);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error recreating cart from channel:', error);
        }
    }

    /**
     * Parse an item embed to extract cart item data
     */
    private static parseItemEmbed(embed: any): CartItem | null {
        try {
            const fields = embed.fields;
            if (!fields || fields.length < 3) return null;

            // Look for both old and new field formats for backward compatibility
            let itemField = fields.find((f: any) => f.name === '📦 Item');
            let buyingField = fields.find((f: any) => f.name.includes('🛒 Buying'));
            let sellingField = fields.find((f: any) => f.name.includes('💰 Selling'));

            const quantityField = fields.find((f: any) => f.name === '🔢 Quantity');

            let paymentField = fields.find((f: any) => f.name === '💳 Payment');
            let receiveField = fields.find((f: any) => f.name.includes('💰 Wants to Receive'));
            let willPayField = fields.find((f: any) => f.name.includes('💳 Will Pay'));

            // Determine listing type and extract data
            let title: string;
            let listingType: 'buy' | 'sell' | undefined;
            let paymentMethod: string;

            if (buyingField) {
                // New format - buying item
                title = buyingField.value.replace(/\*\*/g, '').trim();
                listingType = 'sell'; // User is buying from a selling listing
                paymentMethod = (willPayField?.value || 'Unknown payment').trim();
            } else if (sellingField) {
                // New format - selling item
                title = sellingField.value.replace(/\*\*/g, '').trim();
                listingType = 'buy'; // User is selling to a buying listing
                paymentMethod = (receiveField?.value || 'Unknown payment').trim();
            } else if (itemField) {
                // Old format - fallback
                title = itemField.value.replace(/\*\*/g, '').trim();
                paymentMethod = (paymentField?.value || 'Unknown payment').trim();
                listingType = undefined; // Can't determine from old format
            } else {
                return null;
            }

            if (!quantityField) return null;

            // Parse quantity, removing any commas for consistency
            const quantityStr = quantityField.value.replace(/,/g, '');
            const quantity = parseInt(quantityStr);

            if (isNaN(quantity)) {
                console.error(`Failed to parse quantity: "${quantityField.value}" -> "${quantityStr}"`);
                return null;
            }

            // Generate a unique listing ID based on title (for persistence)
            const listingId = this.generateListingIdFromTitle(title);

            console.log(`[Cart Parse] Parsed item: "${title}" x${quantity} (${paymentMethod}) [${listingType}]`);

            return {
                listingId,
                title,
                quantity,
                paymentMethod,
                listingType
            };
        } catch (error) {
            console.error('Error parsing item embed:', error);
            return null;
        }
    }

    /**
     * Generate a consistent listing ID from title for persistence
     */
    private static generateListingIdFromTitle(title: string): number {
        // Create a simple hash from the title for consistency
        let hash = 0;
        for (let i = 0; i < title.length; i++) {
            const char = title.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    /**
     * Fetch all messages from a channel (optimized for cart recreation)
     */
    private static async fetchAllMessages(channel: TextChannel): Promise<any[]> {
        const messages: any[] = [];
        let lastMessageId: string | undefined;
        let messageCount = 0;
        const maxMessages = 1000; // Limit to prevent excessive API calls

        try {
            console.log(`Fetching messages from cart channel: ${channel.name}`);

            while (messageCount < maxMessages) {
                const fetchedMessages = await channel.messages.fetch({
                    limit: 100,
                    before: lastMessageId,
                    cache: false // Don't cache to save memory
                });

                if (fetchedMessages.size === 0) break;

                // Only collect messages with embeds (item messages) to optimize
                const relevantMessages = fetchedMessages.filter(msg =>
                    msg.embeds.length > 0 &&
                    msg.embeds[0].title === this.ITEM_MESSAGE_IDENTIFIER
                );

                messages.push(...relevantMessages.values());
                messageCount += fetchedMessages.size;
                lastMessageId = fetchedMessages.last()?.id;

                // Add small delay to prevent rate limiting
                if (messageCount % 500 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            console.log(`Found ${messages.length} item messages out of ${messageCount} total messages`);

            // Sort messages by creation time (oldest first)
            return messages.sort((a, b) => a.createdTimestamp - b.createdTimestamp);
        } catch (error) {
            console.error('Error fetching messages:', error);
            return [];
        }
    }

    /**
     * Create a visually appealing summary embed with structured data
     */
    private static createSummaryEmbed(userId: string, guildId: string, userMention: string): EmbedBuilder {
        const cart = cartManager.getCart(userId, guildId);

        const embed = new EmbedBuilder()
            .setTitle('🛒 Trading Summary')
            .setColor('#00ff88')
            .setDescription(`**Trader:** ${userMention}\n**Items in cart:** ${cart?.items.length || 0}`)
            .setTimestamp()
            .setFooter({ text: 'Updated automatically • Cart persists after bot restart' });

        if (cart && cart.items.length > 0) {
            // Separate items by buy/sell type
            const buyingItems = cart.items.filter(item => item.listingType !== 'buy');
            const sellingItems = cart.items.filter(item => item.listingType === 'buy');

            // Add buying section
            if (buyingItems.length > 0) {
                const buyingList = buyingItems.map(item => {
                    const paymentInfo = item.paymentMethod ? ` (${item.paymentMethod})` : '';
                    const formattedQuantity = NumberFormatter.formatWithCommas(item.quantity);
                    const itemEmoji = EmojiService.getItemEmoji(item.title);
                    return `${itemEmoji} **${item.title}** x${formattedQuantity}${paymentInfo}`;
                }).join('\n');

                embed.addFields({
                    name: '🛒 Items You Want to Buy',
                    value: buyingList,
                    inline: false
                });
            }

            // Add selling section
            if (sellingItems.length > 0) {
                const sellingList = sellingItems.map(item => {
                    const receiveInfo = item.paymentMethod ? ` (receive: ${item.paymentMethod})` : '';
                    const formattedQuantity = NumberFormatter.formatWithCommas(item.quantity);
                    const itemEmoji = EmojiService.getItemEmoji(item.title);
                    return `${itemEmoji} **${item.title}** x${formattedQuantity}${receiveInfo}`;
                }).join('\n');

                embed.addFields({
                    name: '💰 Items You Want to Sell',
                    value: sellingList,
                    inline: false
                });
            }

            // Add total summary with buy/sell breakdown
            const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
            const buyingCount = buyingItems.reduce((sum, item) => sum + item.quantity, 0);
            const sellingCount = sellingItems.reduce((sum, item) => sum + item.quantity, 0);

            embed.addFields({
                name: '📊 Total Summary',
                value: `**Total Items:** ${NumberFormatter.formatWithCommas(totalItems)}\n**Buying:** ${NumberFormatter.formatWithCommas(buyingCount)} items\n**Selling:** ${NumberFormatter.formatWithCommas(sellingCount)} items\n**Total Listings:** ${NumberFormatter.formatWithCommas(cart.items.length)}`,
                inline: true
            });
        } else {
            // Show empty cart message
            embed.addFields({
                name: '📭 Empty Cart',
                value: 'No items in cart yet. Browse available listings and click the Buy/Sell buttons to add items.',
                inline: false
            });
        }

        return embed;
    }

    /**
     * Remove ALL summary messages from channel (optimized search)
     */
    private static async removeSummary(channel: TextChannel): Promise<void> {
        try {
            const summaryMessages: any[] = [];

            // First, check recent messages for summaries (most common case)
            const recentMessages = await channel.messages.fetch({ limit: 50, cache: false });
            const recentSummaries = recentMessages.filter(message =>
                message.content.includes(this.SUMMARY_MESSAGE_IDENTIFIER) &&
                message.author.bot
            );

            summaryMessages.push(...recentSummaries.values());

            // If we found summaries in recent messages, also search the entire channel
            // to make sure we don't miss any older ones
            if (recentSummaries.size > 0) {
                console.log(`Found ${recentSummaries.size} recent summary messages, searching entire channel for more`);

                let lastMessageId: string | undefined;
                let searchedCount = 0;
                const maxSearch = 2000; // Limit search to prevent excessive API calls

                while (searchedCount < maxSearch) {
                    const messages = await channel.messages.fetch({
                        limit: 100,
                        before: lastMessageId,
                        cache: false
                    });

                    if (messages.size === 0) break;

                    // Find all summary messages in this batch (excluding ones we already found)
                    const batchSummaries = messages.filter(message =>
                        message.content.includes(this.SUMMARY_MESSAGE_IDENTIFIER) &&
                        message.author.bot &&
                        !summaryMessages.some(existing => existing.id === message.id)
                    );

                    summaryMessages.push(...batchSummaries.values());
                    searchedCount += messages.size;
                    lastMessageId = messages.last()?.id;

                    // Add small delay to prevent rate limiting
                    if (searchedCount % 500 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }
            }

            // Delete all found summary messages
            for (const summaryMessage of summaryMessages) {
                try {
                    await summaryMessage.delete();
                    console.log(`Removed old summary message: ${summaryMessage.id}`);
                } catch (deleteError) {
                    console.error(`Error deleting summary message ${summaryMessage.id}:`, deleteError);
                }
            }

            if (summaryMessages.length > 0) {
                console.log(`Removed ${summaryMessages.length} old summary message(s) from ${channel.name}`);
            }
        } catch (error) {
            console.error('Error removing summary:', error);
        }
    }

    /**
     * Create a structured item embed for individual items (designed for easy parsing)
     */
    static createItemEmbed(
        listingTitle: string,
        quantity: number,
        paymentMethod: string,
        userMention: string,
        listingType?: 'buy' | 'sell'
    ): { embed: EmbedBuilder; components: ActionRowBuilder<ButtonBuilder>[] } {
        // Determine action and color based on listing type
        const isBuyingListing = listingType === 'buy';
        const action = isBuyingListing ? 'wants to sell' : 'wants to buy';
        const actionEmoji = isBuyingListing ? '💰' : '🛒';
        const color = isBuyingListing ? '#ff6b6b' : '#0099ff'; // Red for selling, blue for buying

        // Create a unique identifier for this item based on title, quantity, and payment
        const itemId = this.generateItemId(listingTitle, quantity, paymentMethod, listingType);
        console.log(`[Cart Create] Creating item embed with ID: ${itemId} for "${listingTitle}" x${quantity} (${paymentMethod}) [${listingType}]`);

        const embed = new EmbedBuilder()
            .setTitle(this.ITEM_MESSAGE_IDENTIFIER)
            .setColor(color)
            .setDescription(`${userMention} ${action} an item`)
            .addFields(
                {
                    name: `${actionEmoji} ${isBuyingListing ? 'Selling' : 'Buying'}`,
                    value: `**${listingTitle}**`,
                    inline: true
                },
                {
                    name: '🔢 Quantity',
                    value: `${NumberFormatter.formatWithCommas(quantity)}`,
                    inline: true
                },
                {
                    name: isBuyingListing ? '💰 Wants to Receive' : '💳 Will Pay',
                    value: paymentMethod,
                    inline: true
                }
            )
            .setTimestamp();

        // Create remove button
        const removeButton = new ActionRowBuilder<ButtonBuilder>()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`cart_remove_${itemId}`)
                    .setLabel('Remove Product')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️')
            );

        return {
            embed,
            components: [removeButton]
        };
    }

    /**
     * Generate a unique identifier for cart items
     * Normalized to handle formatting inconsistencies
     */
    private static generateItemId(
        title: string,
        quantity: number,
        paymentMethod: string,
        listingType?: 'buy' | 'sell'
    ): string {
        // Normalize the data to handle formatting inconsistencies
        const normalizedTitle = title.trim().replace(/\*\*/g, ''); // Remove markdown formatting
        const normalizedPayment = paymentMethod.trim();
        const normalizedType = listingType || 'unknown';

        // Create a unique hash based on normalized item properties
        const data = `${normalizedTitle}_${quantity}_${normalizedPayment}_${normalizedType}`;
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
            const char = data.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString();
    }
}
