import { Embed<PERSON><PERSON>er, TextChannel, ActionRowBuilder, ButtonBuilder, ButtonStyle, ThreadChannel } from "discord.js";
import { cartManager, CartItem } from "../utils/cartManager";
import { NumberFormatter } from "../utils/numberFormatter";
import { EmojiService } from "./emoji-service";
import { GuildCartHelper, ItemCategory } from "../utils/guildCartHelper";

export class CartSummaryService {
    private static readonly SUMMARY_MESSAGE_IDENTIFIER = "🛒 **SHOPPING SUMMARY**";
    private static readonly ITEM_MESSAGE_IDENTIFIER = "🛍️ Item Added to Cart";

    /**
     * Create or update the cart summary at the end of the channel
     * Ensures only one summary exists and it's always at the end
     */
    static async updateCartSummary(
        channel: TextChannel,
        userId: string,
        guildId: string,
        userMention: string
    ): Promise<void> {
        try {
            // Always remove existing summary first to ensure only one exists
            await this.removeSummary(channel);

            // Recreate cart from channel messages to ensure persistence after bot restart
            await this.recreateCartFromChannel(channel, userId, guildId);

            // Check if cart is empty and should be closed (after recreation)
            const shouldCloseCart = await this.shouldCloseEmptyCart(channel, userId, guildId);
            if (shouldCloseCart) {
                console.log(`[Cart Management] Cart is empty, closing channel: ${channel.name}`);
                console.log(`[Cart Management] Cart items: ${cartManager.getCart(userId, guildId)?.items.length || 0}`);
                await this.closeEmptyCartChannel(channel, userMention);
                return;
            }

            // Create summary embed (even if cart is empty)
            const summaryEmbed = await this.createSummaryEmbed(userId, guildId, userMention);

            // Always create admin buttons (permission check happens when pressed)
            const adminButtons = new ActionRowBuilder<ButtonBuilder>()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`ticket_transcript`)
                        .setLabel('Save Transcript')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('📝'),
                    new ButtonBuilder()
                        .setCustomId(`ticket_close`)
                        .setLabel('Close Ticket')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('🔒')
                );

            // Always create new summary at the end
            await channel.send({
                content: this.SUMMARY_MESSAGE_IDENTIFIER,
                embeds: [summaryEmbed],
                components: [adminButtons]
            });
        } catch (error) {
            console.error('Error updating cart summary:', error);
        }
    }

    /**
     * Create or update the cart summary for a specific thread
     * Shows only items from that category
     */
    static async updateThreadSummary(
        thread: ThreadChannel,
        userId: string,
        guildId: string,
        userMention: string,
        category: ItemCategory
    ): Promise<void> {
        try {
            // Check if this thread should be closed due to being empty
            const cart = cartManager.getCart(userId, guildId);
            const categoryItems = cart?.items.filter(item => item.category === category) || [];

            if (categoryItems.length === 0) {
                console.log(`[Thread Management] Closing empty thread for category: ${category}`);
                await this.closeEmptyThread(thread, userMention, category);
                return;
            }

            // Always remove existing summary first to ensure only one exists
            await this.removeSummary(thread as any);

            // Create summary embed for this category only
            const summaryEmbed = this.createThreadSummaryEmbed(userId, guildId, userMention, category);

            // Always create admin buttons (permission check happens when pressed)
            const adminButtons = new ActionRowBuilder<ButtonBuilder>()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`ticket_transcript`)
                        .setLabel('Save Transcript')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('📝'),
                    new ButtonBuilder()
                        .setCustomId(`ticket_close`)
                        .setLabel('Close Thread')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('🔒')
                );

            // Always create new summary at the end
            await thread.send({
                content: this.SUMMARY_MESSAGE_IDENTIFIER,
                embeds: [summaryEmbed],
                components: [adminButtons]
            });
        } catch (error) {
            console.error('Error updating thread summary:', error);
        }
    }

    /**
     * Recreate cart from channel messages (for bot restart persistence)
     * Now supports both single cart mode and thread-based cart mode
     */
    static async recreateCartFromChannel(
        channel: TextChannel,
        userId: string,
        guildId: string
    ): Promise<void> {
        try {
            // Clear existing cart first
            cartManager.clearCart(userId, guildId);

            // Check if guild is using thread mode
            const cartMode = await GuildCartHelper.getGuildCartMode(guildId);

            if (cartMode === 'threads') {
                // Thread mode: search both main channel and all threads
                await this.recreateFromChannelAndThreads(channel, userId, guildId);
            } else {
                // Single mode: search only main channel
                await this.recreateFromMainChannel(channel, userId, guildId);
            }
        } catch (error) {
            console.error('Error recreating cart from channel:', error);
        }
    }

    /**
     * Recreate cart from main channel only (single cart mode)
     */
    private static async recreateFromMainChannel(
        channel: TextChannel,
        userId: string,
        guildId: string
    ): Promise<void> {
        try {
            // Fetch all messages from the main channel
            const messages = await this.fetchAllMessages(channel);

            // Parse item messages to recreate cart
            for (const message of messages) {
                if (message.embeds.length > 0) {
                    const embed = message.embeds[0];

                    // Check if this is an item embed
                    if (embed.title === this.ITEM_MESSAGE_IDENTIFIER) {
                        const itemData = this.parseItemEmbed(embed);
                        if (itemData) {
                            console.log(`[Cart Recreation] Adding item to cart: "${itemData.title}" x${itemData.quantity} (${itemData.paymentMethod}) [${itemData.listingType}]`);
                            cartManager.addItem(userId, guildId, itemData);
                        } else {
                            console.log(`[Cart Recreation] Failed to parse item embed from message`);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error recreating cart from main channel:', error);
        }
    }

    /**
     * Recreate cart from main channel and all threads (thread cart mode)
     */
    private static async recreateFromChannelAndThreads(
        channel: TextChannel,
        userId: string,
        guildId: string
    ): Promise<void> {
        try {
            // First, recreate from main channel
            await this.recreateFromMainChannel(channel, userId, guildId);

            // Check if channel has threads property and is a proper TextChannel
            if (!channel.threads) {
                console.log(`[Cart Recreation] Channel ${channel.name} doesn't have threads property, skipping thread search`);
                return;
            }

            // Then, search all threads in the channel
            let threads, archivedThreads;

            try {
                threads = await channel.threads.fetchActive();
                archivedThreads = await channel.threads.fetchArchived();
            } catch (fetchError) {
                console.error('Error fetching threads:', fetchError);
                return;
            }

            // Combine active and archived threads
            const allThreads = new Map([...threads.threads, ...archivedThreads.threads]);

            console.log(`[Cart Recreation] Found ${allThreads.size} threads to search`);

            for (const [, thread] of allThreads) {
                try {
                    // Check if the user is a member of this thread
                    const threadMembers = await thread.members.fetch();
                    if (!threadMembers.has(userId)) {
                        continue; // Skip threads the user isn't part of
                    }

                    console.log(`[Cart Recreation] Searching thread: ${thread.name}`);
                    const threadMessages = await this.fetchAllMessages(thread as ThreadChannel);

                    // Parse item messages from thread
                    for (const message of threadMessages) {
                        if (message.embeds.length > 0) {
                            const embed = message.embeds[0];

                            // Check if this is an item embed
                            if (embed.title === this.ITEM_MESSAGE_IDENTIFIER) {
                                const itemData = this.parseItemEmbed(embed);
                                if (itemData) {
                                    console.log(`[Cart Recreation] Adding item from thread "${thread.name}": "${itemData.title}" x${itemData.quantity} (${itemData.paymentMethod}) [${itemData.listingType}]`);
                                    cartManager.addItem(userId, guildId, itemData);
                                } else {
                                    console.log(`[Cart Recreation] Failed to parse item embed from thread message`);
                                }
                            }
                        }
                    }
                } catch (threadError) {
                    console.error(`Error processing thread ${thread.name}:`, threadError);
                }
            }
        } catch (error) {
            console.error('Error recreating cart from channel and threads:', error);
        }
    }

    /**
     * Parse an item embed to extract cart item data
     */
    private static parseItemEmbed(embed: any): CartItem | null {
        try {
            const fields = embed.fields;
            if (!fields || fields.length < 3) return null;

            // Look for both old and new field formats for backward compatibility
            let itemField = fields.find((f: any) => f.name === '📦 Item');
            let buyingField = fields.find((f: any) => f.name.includes('🛒 Buying'));
            let sellingField = fields.find((f: any) => f.name.includes('💰 Selling'));

            const quantityField = fields.find((f: any) => f.name === '🔢 Quantity');

            let paymentField = fields.find((f: any) => f.name === '💳 Payment');
            let receiveField = fields.find((f: any) => f.name.includes('💰 Wants to Receive'));
            let willPayField = fields.find((f: any) => f.name.includes('💳 Will Pay'));

            // Determine listing type and extract data
            let title: string;
            let listingType: 'buy' | 'sell' | undefined;
            let paymentMethod: string;

            if (buyingField) {
                // New format - buying item
                title = buyingField.value.replace(/\*\*/g, '').trim();
                listingType = 'sell'; // User is buying from a selling listing
                paymentMethod = (willPayField?.value || 'Unknown payment').trim();
            } else if (sellingField) {
                // New format - selling item
                title = sellingField.value.replace(/\*\*/g, '').trim();
                listingType = 'buy'; // User is selling to a buying listing
                paymentMethod = (receiveField?.value || 'Unknown payment').trim();
            } else if (itemField) {
                // Old format - fallback
                title = itemField.value.replace(/\*\*/g, '').trim();
                paymentMethod = (paymentField?.value || 'Unknown payment').trim();
                listingType = undefined; // Can't determine from old format
            } else {
                return null;
            }

            if (!quantityField) return null;

            // Parse quantity, removing any commas for consistency
            const quantityStr = quantityField.value.replace(/,/g, '');
            const quantity = parseInt(quantityStr);

            if (isNaN(quantity)) {
                console.error(`Failed to parse quantity: "${quantityField.value}" -> "${quantityStr}"`);
                return null;
            }

            // Generate a unique listing ID based on title (for persistence)
            const listingId = this.generateListingIdFromTitle(title);

            console.log(`[Cart Parse] Parsed item: "${title}" x${quantity} (${paymentMethod}) [${listingType}]`);

            return {
                listingId,
                title,
                quantity,
                paymentMethod,
                listingType,
                category: 'other' // Default category for backward compatibility
            };
        } catch (error) {
            console.error('Error parsing item embed:', error);
            return null;
        }
    }

    /**
     * Generate a consistent listing ID from title for persistence
     */
    private static generateListingIdFromTitle(title: string): number {
        // Create a simple hash from the title for consistency
        let hash = 0;
        for (let i = 0; i < title.length; i++) {
            const char = title.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    /**
     * Fetch all messages from a channel or thread (optimized for cart recreation)
     */
    private static async fetchAllMessages(channel: TextChannel | ThreadChannel): Promise<any[]> {
        const messages: any[] = [];
        let lastMessageId: string | undefined;
        let messageCount = 0;
        const maxMessages = 1000; // Limit to prevent excessive API calls

        try {
            console.log(`Fetching messages from cart channel: ${channel.name}`);

            while (messageCount < maxMessages) {
                const fetchedMessages = await channel.messages.fetch({
                    limit: 100,
                    before: lastMessageId,
                    cache: false // Don't cache to save memory
                });

                if (fetchedMessages.size === 0) break;

                // Only collect messages with embeds (item messages) to optimize
                const relevantMessages = fetchedMessages.filter(msg =>
                    msg.embeds.length > 0 &&
                    msg.embeds[0].title === this.ITEM_MESSAGE_IDENTIFIER
                );

                messages.push(...relevantMessages.values());
                messageCount += fetchedMessages.size;
                lastMessageId = fetchedMessages.last()?.id;

                // Add small delay to prevent rate limiting
                if (messageCount % 500 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            console.log(`Found ${messages.length} item messages out of ${messageCount} total messages`);

            // Sort messages by creation time (oldest first)
            return messages.sort((a, b) => a.createdTimestamp - b.createdTimestamp);
        } catch (error) {
            console.error('Error fetching messages:', error);
            return [];
        }
    }

    /**
     * Create a summary embed for a specific thread category
     */
    private static createThreadSummaryEmbed(
        userId: string,
        guildId: string,
        userMention: string,
        category: ItemCategory
    ): EmbedBuilder {
        const cart = cartManager.getCart(userId, guildId);
        const categoryItems = cart?.items.filter(item => item.category === category) || [];

        const categoryNames: Record<ItemCategory, string> = {
            item: 'Items',
            dino: 'Dinosaurs',
            egg: 'Eggs',
            blueprint: 'Blueprints',
            boss_fight: 'Boss Fights',
            base_spot: 'Base Spots',
            xp_party: 'XP Parties',
            other: 'Other',
            bundle: 'Bundles'
        };

        const categoryEmojis: Record<ItemCategory, string> = {
            item: '🎒',
            dino: '🦕',
            egg: '🥚',
            blueprint: '📋',
            boss_fight: '⚔️',
            base_spot: '🏠',
            xp_party: '🎉',
            other: '📦',
            bundle: '🎁'
        };

        const categoryName = categoryNames[category];
        const categoryEmoji = categoryEmojis[category];

        const embed = new EmbedBuilder()
            .setTitle(`${categoryEmoji} ${categoryName} Summary`)
            .setColor('#00ff88')
            .setDescription(`**Trader:** ${userMention}\n**${categoryName} in cart:** ${categoryItems.length}`)
            .setTimestamp()
            .setFooter({ text: 'Thread-specific summary • Cart persists after bot restart' });

        if (categoryItems.length > 0) {
            // Separate items by buy/sell type
            const buyingItems = categoryItems.filter(item => item.listingType !== 'buy');
            const sellingItems = categoryItems.filter(item => item.listingType === 'buy');

            // Add buying section
            if (buyingItems.length > 0) {
                const buyingList = buyingItems.map(item => {
                    const paymentInfo = item.paymentMethod ? ` (${item.paymentMethod})` : '';
                    const formattedQuantity = NumberFormatter.formatWithCommas(item.quantity);
                    const itemEmoji = EmojiService.getItemEmoji(item.title);
                    return `${itemEmoji} **${item.title}** x${formattedQuantity}${paymentInfo}`;
                }).join('\n');

                embed.addFields({
                    name: `🛒 ${categoryName} You Want to Buy`,
                    value: buyingList,
                    inline: false
                });
            }

            // Add selling section
            if (sellingItems.length > 0) {
                const sellingList = sellingItems.map(item => {
                    const receiveInfo = item.paymentMethod ? ` (receive: ${item.paymentMethod})` : '';
                    const formattedQuantity = NumberFormatter.formatWithCommas(item.quantity);
                    const itemEmoji = EmojiService.getItemEmoji(item.title);
                    return `${itemEmoji} **${item.title}** x${formattedQuantity}${receiveInfo}`;
                }).join('\n');

                embed.addFields({
                    name: `💰 ${categoryName} You Want to Sell`,
                    value: sellingList,
                    inline: false
                });
            }

            // Add total summary for this category
            const totalItems = categoryItems.reduce((sum, item) => sum + item.quantity, 0);
            const buyingCount = buyingItems.reduce((sum, item) => sum + item.quantity, 0);
            const sellingCount = sellingItems.reduce((sum, item) => sum + item.quantity, 0);

            embed.addFields({
                name: '📊 Category Summary',
                value: `**Total ${categoryName}:** ${NumberFormatter.formatWithCommas(totalItems)}\n**Buying:** ${NumberFormatter.formatWithCommas(buyingCount)} items\n**Selling:** ${NumberFormatter.formatWithCommas(sellingCount)} items\n**Total Listings:** ${NumberFormatter.formatWithCommas(categoryItems.length)}`,
                inline: true
            });
        } else {
            // Show empty category message
            embed.addFields({
                name: `📭 No ${categoryName}`,
                value: `No ${categoryName.toLowerCase()} in cart yet. Browse available listings and add ${categoryName.toLowerCase()} to this thread!`,
                inline: false
            });
        }

        return embed;
    }

    /**
     * Create a visually appealing summary embed with structured data
     * In thread mode, shows overview with thread information
     */
    private static async createSummaryEmbed(userId: string, guildId: string, userMention: string): Promise<EmbedBuilder> {
        const cart = cartManager.getCart(userId, guildId);
        const cartMode = await GuildCartHelper.getGuildCartMode(guildId);

        const embed = new EmbedBuilder()
            .setTitle('🛒 Trading Summary')
            .setColor('#00ff88')
            .setDescription(`**Trader:** ${userMention}\n**Items in cart:** ${cart?.items.length || 0}`)
            .setTimestamp()
            .setFooter({ text: 'Updated automatically • Cart persists after bot restart' });

        if (cartMode === 'threads') {
            // Thread mode: Show overview by category
            if (cart && cart.items.length > 0) {
                const itemsByCategory = cartManager.getItemsGroupedByCategory(userId, guildId);
                const categoriesWithItems = Object.entries(itemsByCategory).filter(([, items]) => items.length > 0);

                if (categoriesWithItems.length > 0) {
                    const categoryNames: Record<ItemCategory, string> = {
                        item: 'Items',
                        dino: 'Dinosaurs',
                        egg: 'Eggs',
                        blueprint: 'Blueprints',
                        boss_fight: 'Boss Fights',
                        base_spot: 'Base Spots',
                        xp_party: 'XP Parties',
                        other: 'Other',
                        bundle: 'Bundles'
                    };

                    const categoryEmojis: Record<ItemCategory, string> = {
                        item: '🎒',
                        dino: '🦕',
                        egg: '🥚',
                        blueprint: '📋',
                        boss_fight: '⚔️',
                        base_spot: '🏠',
                        xp_party: '🎉',
                        other: '📦',
                        bundle: '🎁'
                    };

                    const categoryOverview = categoriesWithItems.map(([category, items]) => {
                        const categoryName = categoryNames[category as ItemCategory];
                        const categoryEmoji = categoryEmojis[category as ItemCategory];
                        const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);
                        return `${categoryEmoji} **${categoryName}**: ${NumberFormatter.formatWithCommas(totalQuantity)} items (${items.length} listings)`;
                    }).join('\n');

                    embed.addFields({
                        name: '🧵 Items by Category Thread',
                        value: categoryOverview + '\n\n*Check individual category threads for detailed summaries and management options.*',
                        inline: false
                    });

                    // Add total summary
                    const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
                    const buyingItems = cart.items.filter(item => item.listingType !== 'buy');
                    const sellingItems = cart.items.filter(item => item.listingType === 'buy');
                    const buyingCount = buyingItems.reduce((sum, item) => sum + item.quantity, 0);
                    const sellingCount = sellingItems.reduce((sum, item) => sum + item.quantity, 0);

                    embed.addFields({
                        name: '📊 Total Summary',
                        value: `**Total Items:** ${NumberFormatter.formatWithCommas(totalItems)}\n**Buying:** ${NumberFormatter.formatWithCommas(buyingCount)} items\n**Selling:** ${NumberFormatter.formatWithCommas(sellingCount)} items\n**Categories:** ${categoriesWithItems.length}`,
                        inline: true
                    });
                } else {
                    embed.addFields({
                        name: '📭 Empty Cart',
                        value: 'No items in cart yet. Browse available listings and add items to category threads!',
                        inline: false
                    });
                }
            } else {
                embed.addFields({
                    name: '📭 Empty Cart',
                    value: 'No items in cart yet. Browse available listings and add items to category threads!',
                    inline: false
                });
            }
        } else {
            // Single mode: Show all items as before
            if (cart && cart.items.length > 0) {
                // Separate items by buy/sell type
                const buyingItems = cart.items.filter(item => item.listingType !== 'buy');
                const sellingItems = cart.items.filter(item => item.listingType === 'buy');

                // Add buying section
                if (buyingItems.length > 0) {
                    const buyingList = buyingItems.map(item => {
                        const paymentInfo = item.paymentMethod ? ` (${item.paymentMethod})` : '';
                        const formattedQuantity = NumberFormatter.formatWithCommas(item.quantity);
                        const itemEmoji = EmojiService.getItemEmoji(item.title);
                        return `${itemEmoji} **${item.title}** x${formattedQuantity}${paymentInfo}`;
                    }).join('\n');

                    embed.addFields({
                        name: '🛒 Items You Want to Buy',
                        value: buyingList,
                        inline: false
                    });
                }

                // Add selling section
                if (sellingItems.length > 0) {
                    const sellingList = sellingItems.map(item => {
                        const receiveInfo = item.paymentMethod ? ` (receive: ${item.paymentMethod})` : '';
                        const formattedQuantity = NumberFormatter.formatWithCommas(item.quantity);
                        const itemEmoji = EmojiService.getItemEmoji(item.title);
                        return `${itemEmoji} **${item.title}** x${formattedQuantity}${receiveInfo}`;
                    }).join('\n');

                    embed.addFields({
                        name: '💰 Items You Want to Sell',
                        value: sellingList,
                        inline: false
                    });
                }

                // Add total summary with buy/sell breakdown
                const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
                const buyingCount = buyingItems.reduce((sum, item) => sum + item.quantity, 0);
                const sellingCount = sellingItems.reduce((sum, item) => sum + item.quantity, 0);

                embed.addFields({
                    name: '📊 Total Summary',
                    value: `**Total Items:** ${NumberFormatter.formatWithCommas(totalItems)}\n**Buying:** ${NumberFormatter.formatWithCommas(buyingCount)} items\n**Selling:** ${NumberFormatter.formatWithCommas(sellingCount)} items\n**Total Listings:** ${NumberFormatter.formatWithCommas(cart.items.length)}`,
                    inline: true
                });
            } else {
                // Show empty cart message
                embed.addFields({
                    name: '📭 Empty Cart',
                    value: 'No items in cart yet. Browse available listings and click the Buy/Sell buttons to add items.',
                    inline: false
                });
            }
        }

        return embed;
    }

    /**
     * Check if a cart should be closed due to being empty
     * In thread mode: checks if there are no active threads with items
     * In single mode: checks if cart is empty
     */
    private static async shouldCloseEmptyCart(
        channel: TextChannel,
        userId: string,
        guildId: string
    ): Promise<boolean> {
        try {
            const cart = cartManager.getCart(userId, guildId);
            const cartMode = await GuildCartHelper.getGuildCartMode(guildId);

            // If cart doesn't exist or is empty, consider closing
            if (!cart || cart.items.length === 0) {
                if (cartMode === 'threads') {
                    // In thread mode, check if there are any active threads
                    if (!channel.threads) {
                        return true; // No threads capability, cart is empty
                    }

                    try {
                        const activeThreads = await channel.threads.fetchActive();
                        const hasActiveThreads = activeThreads.threads.size > 0;

                        console.log(`[Cart Management] Cart empty, active threads: ${activeThreads.threads.size}`);
                        return !hasActiveThreads; // Close if no active threads
                    } catch (error) {
                        console.error('Error checking active threads:', error);
                        return true; // If we can't check threads, assume we should close
                    }
                } else {
                    // In single mode, close if cart is empty
                    return true;
                }
            }

            return false; // Don't close if cart has items
        } catch (error) {
            console.error('Error checking if cart should be closed:', error);
            return false; // Don't close on error to be safe
        }
    }

    /**
     * Close an empty thread with a farewell message
     */
    private static async closeEmptyThread(
        thread: ThreadChannel,
        userMention: string,
        category: ItemCategory
    ): Promise<void> {
        try {
            const categoryNames: Record<ItemCategory, string> = {
                item: 'Items',
                dino: 'Dinosaurs',
                egg: 'Eggs',
                blueprint: 'Blueprints',
                boss_fight: 'Boss Fights',
                base_spot: 'Base Spots',
                xp_party: 'XP Parties',
                other: 'Other',
                bundle: 'Bundles'
            };

            const categoryName = categoryNames[category];

            // Send farewell message
            await thread.send({
                content: `👋 **${categoryName} Thread Closed**\n\n${userMention}, this ${categoryName.toLowerCase()} thread has been automatically closed because it's empty.\n\nYou can add more ${categoryName.toLowerCase()} anytime and a new thread will be created automatically!`,
            });

            // Wait a moment for the message to be sent
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Archive and lock the thread
            await thread.setArchived(true, `${categoryName} thread automatically closed - empty`);
        } catch (error) {
            console.error('Error closing empty thread:', error);
        }
    }

    /**
     * Close an empty cart channel with a farewell message
     */
    private static async closeEmptyCartChannel(
        channel: TextChannel,
        userMention: string
    ): Promise<void> {
        try {
            // Send farewell message
            await channel.send({
                content: `👋 **Cart Closed**\n\n${userMention}, your cart has been automatically closed because it's empty and has no active category threads.\n\nThank you for using our trading system! Feel free to create a new cart anytime by browsing our listings.`,
            });

            // Wait a moment for the message to be sent
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Delete the channel
            await channel.delete('Cart automatically closed - empty with no active threads');
        } catch (error) {
            console.error('Error closing empty cart channel:', error);
        }
    }

    /**
     * Remove ALL summary messages from channel (optimized search)
     */
    private static async removeSummary(channel: TextChannel): Promise<void> {
        try {
            const summaryMessages: any[] = [];

            // First, check recent messages for summaries (most common case)
            const recentMessages = await channel.messages.fetch({ limit: 50, cache: false });
            const recentSummaries = recentMessages.filter(message =>
                message.content.includes(this.SUMMARY_MESSAGE_IDENTIFIER) &&
                message.author.bot
            );

            summaryMessages.push(...recentSummaries.values());

            // If we found summaries in recent messages, also search the entire channel
            // to make sure we don't miss any older ones
            if (recentSummaries.size > 0) {
                console.log(`Found ${recentSummaries.size} recent summary messages, searching entire channel for more`);

                let lastMessageId: string | undefined;
                let searchedCount = 0;
                const maxSearch = 2000; // Limit search to prevent excessive API calls

                while (searchedCount < maxSearch) {
                    const messages = await channel.messages.fetch({
                        limit: 100,
                        before: lastMessageId,
                        cache: false
                    });

                    if (messages.size === 0) break;

                    // Find all summary messages in this batch (excluding ones we already found)
                    const batchSummaries = messages.filter(message =>
                        message.content.includes(this.SUMMARY_MESSAGE_IDENTIFIER) &&
                        message.author.bot &&
                        !summaryMessages.some(existing => existing.id === message.id)
                    );

                    summaryMessages.push(...batchSummaries.values());
                    searchedCount += messages.size;
                    lastMessageId = messages.last()?.id;

                    // Add small delay to prevent rate limiting
                    if (searchedCount % 500 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }
            }

            // Delete all found summary messages
            for (const summaryMessage of summaryMessages) {
                try {
                    await summaryMessage.delete();
                    console.log(`Removed old summary message: ${summaryMessage.id}`);
                } catch (deleteError) {
                    console.error(`Error deleting summary message ${summaryMessage.id}:`, deleteError);
                }
            }

            if (summaryMessages.length > 0) {
                console.log(`Removed ${summaryMessages.length} old summary message(s) from ${channel.name}`);
            }
        } catch (error) {
            console.error('Error removing summary:', error);
        }
    }

    /**
     * Create a structured item embed for individual items (designed for easy parsing)
     */
    static createItemEmbed(
        listingTitle: string,
        quantity: number,
        paymentMethod: string,
        userMention: string,
        listingType?: 'buy' | 'sell'
    ): { embed: EmbedBuilder; components: ActionRowBuilder<ButtonBuilder>[] } {
        // Determine action and color based on listing type
        const isBuyingListing = listingType === 'buy';
        const action = isBuyingListing ? 'wants to sell' : 'wants to buy';
        const actionEmoji = isBuyingListing ? '💰' : '🛒';
        const color = isBuyingListing ? '#ff6b6b' : '#0099ff'; // Red for selling, blue for buying

        // Create a unique identifier for this item based on title, quantity, and payment
        const itemId = this.generateItemId(listingTitle, quantity, paymentMethod, listingType);
        console.log(`[Cart Create] Creating item embed with ID: ${itemId} for "${listingTitle}" x${quantity} (${paymentMethod}) [${listingType}]`);

        const embed = new EmbedBuilder()
            .setTitle(this.ITEM_MESSAGE_IDENTIFIER)
            .setColor(color)
            .setDescription(`${userMention} ${action} an item`)
            .addFields(
                {
                    name: `${actionEmoji} ${isBuyingListing ? 'Selling' : 'Buying'}`,
                    value: `**${listingTitle}**`,
                    inline: true
                },
                {
                    name: '🔢 Quantity',
                    value: `${NumberFormatter.formatWithCommas(quantity)}`,
                    inline: true
                },
                {
                    name: isBuyingListing ? '💰 Wants to Receive' : '💳 Will Pay',
                    value: paymentMethod,
                    inline: true
                }
            )
            .setTimestamp();

        // Create remove button
        const removeButton = new ActionRowBuilder<ButtonBuilder>()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`cart_remove_${itemId}`)
                    .setLabel('Remove Product')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️')
            );

        return {
            embed,
            components: [removeButton]
        };
    }

    /**
     * Generate a unique identifier for cart items
     * Normalized to handle formatting inconsistencies
     */
    private static generateItemId(
        title: string,
        quantity: number,
        paymentMethod: string,
        listingType?: 'buy' | 'sell'
    ): string {
        // Normalize the data to handle formatting inconsistencies
        const normalizedTitle = title.trim().replace(/\*\*/g, ''); // Remove markdown formatting
        const normalizedPayment = paymentMethod.trim();
        const normalizedType = listingType || 'unknown';

        // Create a unique hash based on normalized item properties
        const data = `${normalizedTitle}_${quantity}_${normalizedPayment}_${normalizedType}`;
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
            const char = data.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString();
    }
}
