import {
    ChannelType,
    EmbedBuilder,
    Guild,
    PermissionFlagsBits,
    TextChannel,
    User,
    AttachmentBuilder
} from "discord.js";
import * as discordTranscripts from 'discord-html-transcripts';
import { ExportReturnType } from 'discord-html-transcripts';

export class TranscriptService {
    // Configuration - admin transcripts channel name
    private static readonly ADMIN_TRANSCRIPTS_CHANNEL_NAME = "📕┊admin-transcripts";

    // Admin role names that should have access to transcripts
    private static readonly ADMIN_ROLES = [
        'admin',
        'administrator',
        'moderator',
        'mod',
        'staff'
    ];

    /**
     * Get admin roles from the guild that should have access to transcripts
     */
    private static getAdminRoles(guild: Guild): string[] {
        const adminRoleIds: string[] = [];

        // Add roles that have Administrator permission
        guild.roles.cache.forEach(role => {
            if (role.permissions.has(PermissionFlagsBits.Administrator) ||
                role.permissions.has(PermissionFlagsBits.ManageGuild) ||
                role.permissions.has(PermissionFlagsBits.ManageMessages)) {
                adminRoleIds.push(role.id);
            }
        });

        // Add roles that match admin role names
        guild.roles.cache.forEach(role => {
            if (this.ADMIN_ROLES.some(adminRole =>
                role.name.toLowerCase().includes(adminRole.toLowerCase())
            )) {
                if (!adminRoleIds.includes(role.id)) {
                    adminRoleIds.push(role.id);
                }
            }
        });

        return adminRoleIds;
    }

    /**
     * Ensure existing transcript channel has admin-only permissions
     */
    private static async ensureAdminOnlyPermissions(channel: TextChannel, guild: Guild): Promise<void> {
        try {
            // Check if @everyone can view the channel
            const everyonePermissions = channel.permissionOverwrites.cache.get(guild.id);
            const canEveryoneView = !everyonePermissions ||
                                   !everyonePermissions.deny.has(PermissionFlagsBits.ViewChannel);

            // Check if bot has proper permissions
            const botPermissions = channel.permissionOverwrites.cache.get(guild.client.user.id);
            const botHasAccess = botPermissions &&
                               botPermissions.allow.has(PermissionFlagsBits.ViewChannel) &&
                               botPermissions.allow.has(PermissionFlagsBits.SendMessages);

            // Update permissions if @everyone can view OR if bot doesn't have proper access
            if (canEveryoneView || !botHasAccess) {
                console.log('Updating transcript channel permissions...');

                // Get admin roles that should have access
                const adminRoleIds = this.getAdminRoles(guild);

                // Deny @everyone from viewing
                await channel.permissionOverwrites.edit(guild.id, {
                    ViewChannel: false
                });

                // Ensure bot has access
                await channel.permissionOverwrites.edit(guild.client.user.id, {
                    ViewChannel: true,
                    SendMessages: true,
                    ReadMessageHistory: true
                });

                // Give admin roles access
                for (const roleId of adminRoleIds) {
                    await channel.permissionOverwrites.edit(roleId, {
                        ViewChannel: true,
                        SendMessages: true,
                        ReadMessageHistory: true
                    });
                }

                console.log('Transcript channel permissions updated successfully');
            }
        } catch (error) {
            console.error('Error updating transcript channel permissions:', error);
        }
    }

    /**
     * Save a ticket transcript to the admin transcripts channel using discord-html-transcripts
     */
    static async saveTicketTranscript(
        guild: Guild,
        ticketChannel: TextChannel,
        requestedBy: User,
        isAutoSave: boolean = false
    ): Promise<boolean> {
        try {
            console.log(`Starting transcript generation for channel: ${ticketChannel.name}`);

            // Find or create the admin transcripts channel
            const transcriptsChannel = await this.getOrCreateTranscriptsChannel(guild);
            if (!transcriptsChannel) {
                console.error('Could not create or find admin transcripts channel');
                return false;
            }

            // Generate HTML transcript using discord-html-transcripts
            console.log('Generating HTML transcript...');
            const attachment = await discordTranscripts.createTranscript(ticketChannel, {
                limit: -1, // Fetch all messages
                returnType: ExportReturnType.Attachment,
                filename: `${ticketChannel.name}-transcript-${Date.now()}.html`,
                saveImages: true, // Include images in the HTML file
                footerText: "Exported {number} message{s}",
                poweredBy: true
            });

            // Send transcript directly to the channel with separator
            await this.sendTranscriptToChannel(transcriptsChannel, attachment, ticketChannel, requestedBy, isAutoSave);

            console.log(`Transcript saved successfully to channel: ${transcriptsChannel.name}`);
            return true;
        } catch (error) {
            console.error('Error saving ticket transcript:', error);
            return false;
        }
    }

    /**
     * Find or create the admin transcripts channel
     */
    private static async getOrCreateTranscriptsChannel(guild: Guild): Promise<TextChannel | null> {
        try {
            // Look for existing channel
            let transcriptsChannel = guild.channels.cache.find(
                channel => channel.name === this.ADMIN_TRANSCRIPTS_CHANNEL_NAME &&
                          channel.type === ChannelType.GuildText
            ) as TextChannel;

            // Create if it doesn't exist
            if (!transcriptsChannel) {
                console.log('Creating admin transcripts text channel...');

                // Get admin roles that should have access
                const adminRoleIds = this.getAdminRoles(guild);

                // Create permission overwrites
                const permissionOverwrites = [
                    {
                        id: guild.id, // @everyone role
                        deny: [PermissionFlagsBits.ViewChannel]
                    },
                    {
                        id: guild.client.user.id, // Bot user
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory
                        ]
                    }
                ];

                // Add permissions for admin roles
                adminRoleIds.forEach(roleId => {
                    permissionOverwrites.push({
                        id: roleId,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory
                        ]
                    });
                });

                transcriptsChannel = await guild.channels.create({
                    name: this.ADMIN_TRANSCRIPTS_CHANNEL_NAME,
                    type: ChannelType.GuildText,
                    topic: 'Ticket transcripts are automatically saved here (Admin Only)',
                    permissionOverwrites
                }) as TextChannel;
                console.log('Admin transcripts channel created successfully with admin-only permissions');
            } else {
                // Check if existing channel has proper admin-only permissions
                await this.ensureAdminOnlyPermissions(transcriptsChannel, guild);
            }

            return transcriptsChannel;
        } catch (error) {
            console.error('Error getting/creating transcripts channel:', error);
            return null;
        }
    }

    /**
     * Send transcript content directly to the channel with separator
     */
    private static async sendTranscriptToChannel(
        transcriptsChannel: TextChannel,
        attachment: AttachmentBuilder | string | Buffer,
        originalChannel: TextChannel,
        requestedBy: User,
        isAutoSave: boolean = false
    ): Promise<void> {
        try {
            // Send separator at the start
            const separatorStart = '═'.repeat(50);
            await transcriptsChannel.send(`\`\`\`\n${separatorStart}\n📝 NEW TRANSCRIPT\n${separatorStart}\n\`\`\``);

            // Send header embed with transcript file
            const title = isAutoSave ? '📝 Ticket Transcript (Auto-saved)' : '📝 Ticket Transcript';
            const description = isAutoSave
                ? 'Complete conversation history automatically saved during channel closure'
                : 'Complete conversation history saved as HTML file';
            const requestType = isAutoSave ? '🔄 Auto-saved by' : '👤 Requested by';

            const headerEmbed = new EmbedBuilder()
                .setTitle(title)
                .setDescription(description)
                .setColor('#4CAF50')
                .addFields(
                    { name: '🎫 Original Channel', value: originalChannel.name, inline: true },
                    { name: requestType, value: `<@${requestedBy.id}>`, inline: true },
                    { name: '⏰ Generated', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: false },
                    { name: '📄 Format', value: 'HTML with embedded images and full Discord formatting', inline: false }
                )
                .setTimestamp();

            await transcriptsChannel.send({
                embeds: [headerEmbed],
                files: [attachment]
            });

            // Send completion message
            const completionEmbed = new EmbedBuilder()
                .setTitle('✅ Transcript Complete')
                .setDescription('HTML transcript generated successfully.')
                .setColor('#00FF00')
                .setTimestamp();

            await transcriptsChannel.send({ embeds: [completionEmbed] });

            // Send separator at the end
            const separatorEnd = '═'.repeat(50);
            await transcriptsChannel.send(`\`\`\`\n${separatorEnd}\n📋 END OF TRANSCRIPT\n${separatorEnd}\n\`\`\``);
        } catch (error) {
            console.error('Error sending transcript to channel:', error);
        }
    }
}
