import {
  ApplicationCommandOptionType,
  ApplicationCommandType,
  CommandInteraction,
  EmbedBuilder,
  Client
} from "discord.js";
import { Command } from "../../types";
import { GuildCartHelper, CartMode } from "../../utils/guildCartHelper";

export const configurecart: Command = {
  name: "configurecart",
  description: "Configure cart system mode for your server (Guild Owner Only)",
  category: "admin",
  type: ApplicationCommandType.ChatInput,
  options: [
    {
      name: "action",
      description: "What action to perform",
      type: ApplicationCommandOptionType.String,
      required: true,
      choices: [
        { name: "View Current Settings", value: "view" },
        { name: "Show Help & Info", value: "help" },
        { name: "Set Single Cart Mode", value: "set_single" },
        { name: "Set Thread Cart Mode", value: "set_threads" }
      ]
    }
  ],

  run: async (_client: Client, interaction: CommandInteraction) => {
    // Check if user is guild owner
    if (!interaction.guild || interaction.user.id !== interaction.guild.ownerId) {
      await interaction.reply({
        content: "❌ Only the server owner can configure cart settings.",
        ephemeral: true
      });
      return;
    }

    await interaction.deferReply({ ephemeral: true });

    const action = interaction.options.get("action", true).value as string;

    try {
      switch (action) {
        case "view":
          await handleViewSettings(interaction);
          break;
        case "help":
          await handleShowHelp(interaction);
          break;
        case "set_single":
          await handleSetCartMode(interaction, "single");
          break;
        case "set_threads":
          await handleSetCartMode(interaction, "threads");
          break;
        default:
          await interaction.editReply({
            content: "❌ Unknown action."
          });
      }
    } catch (error) {
      console.error("Error in configurecart command:", error);
      await interaction.editReply({
        content: "❌ An error occurred while configuring cart settings."
      });
    }
  }
};

async function handleViewSettings(interaction: CommandInteraction) {
  if (!interaction.guild) return;

  const currentMode = await GuildCartHelper.getGuildCartMode(interaction.guild.id);

  const embed = new EmbedBuilder()
    .setTitle("🛒 Cart System Configuration")
    .setDescription("Current cart system settings for this server")
    .setColor(0x3498db)
    .setTimestamp();

  const modeDescription = currentMode === 'single' 
    ? "**Single Cart Mode** - All items are added to one cart channel"
    : "**Thread Cart Mode** - Items are split into category-specific threads";

  const modeEmoji = currentMode === 'single' ? '📦' : '🧵';

  embed.addFields(
    {
      name: `${modeEmoji} Current Cart Mode`,
      value: modeDescription,
      inline: false
    },
    {
      name: "ℹ️ Quick Info",
      value: "• **Single Cart**: Traditional system, all items in main cart channel\n" +
             "• **Thread Cart**: Items split by category into separate threads with role-based access\n" +
             "• Use `/configurecart action:Show Help & Info` for detailed explanations",
      inline: false
    }
  );

  if (currentMode === 'threads') {
    embed.addFields({
      name: "⚙️ Thread Mode Active",
      value: "Use `/configureroles` to set up category-specific permissions for different roles.\n" +
             "Each category (Items, Dinos, Eggs, etc.) can have different role access.",
      inline: false
    });
  }

  await interaction.editReply({ embeds: [embed] });
}

async function handleSetCartMode(interaction: CommandInteraction, mode: CartMode) {
  if (!interaction.guild) return;

  const success = await GuildCartHelper.setGuildCartMode(interaction.guild.id, mode);

  if (success) {
    const modeText = mode === 'single' ? 'Single Cart Mode' : 'Thread Cart Mode';
    const modeDescription = mode === 'single' 
      ? 'All items will be added to one cart channel (traditional system)'
      : 'Items will be split into category-specific threads with role-based access';

    const embed = new EmbedBuilder()
      .setTitle("✅ Cart Mode Updated")
      .setDescription(`Successfully set cart mode to **${modeText}**`)
      .setColor(0x00ff00)
      .addFields({
        name: "What this means:",
        value: modeDescription,
        inline: false
      })
      .setTimestamp();

    if (mode === 'threads') {
      embed.addFields({
        name: "⚠️ Next Steps for Thread Mode:",
        value: "1. Use `/configureroles` to set up category permissions\n" +
               "2. Add roles for each category (Items, Dinos, Eggs, etc.)\n" +
               "3. New cart channels will automatically create category threads\n" +
               "4. Users will only see threads for categories they have access to",
        inline: false
      });
    }

    await interaction.editReply({ embeds: [embed] });
  } else {
    await interaction.editReply({
      content: "❌ Failed to update cart mode. Please try again."
    });
  }
}

async function handleShowHelp(interaction: CommandInteraction) {
  const embed = new EmbedBuilder()
    .setTitle("🛒 Cart System Configuration Help")
    .setDescription("Detailed explanation of cart system modes and how to configure them")
    .setColor(0x3498db)
    .setTimestamp();

  embed.addFields(
    {
      name: "📦 **Single Cart Mode** (Default)",
      value: "• **Traditional system** - All items go into one cart channel\n" +
             "• **Simple setup** - No additional configuration needed\n" +
             "• **All users see everything** - No category restrictions\n" +
             "• **Best for**: Small servers, simple trading setups\n" +
             "• **Example**: Customer adds dino, items, eggs all in same cart",
      inline: false
    },
    {
      name: "🧵 **Thread Cart Mode** (Advanced)",
      value: "• **Category-based threads** - Items split by category (Items, Dinos, Eggs, etc.)\n" +
             "• **Role-based access** - Different roles see different category threads\n" +
             "• **Enhanced security** - Prevents cross-category selling by unauthorized roles\n" +
             "• **Best for**: Large servers, specialized sellers, security-focused setups\n" +
             "• **Example**: Boss Fight sellers only see boss fight thread, Egg sellers only see egg thread",
      inline: false
    },
    {
      name: "⚙️ **Setting Up Thread Mode**",
      value: "1. **Enable Thread Mode**: `/configurecart action:Set Thread Cart Mode`\n" +
             "2. **Configure Permissions**: Use `/configureroles` to set category permissions\n" +
             "3. **Add Category Roles**: Assign roles to specific categories\n" +
             "4. **Test**: Create a new cart to see category threads in action",
      inline: false
    },
    {
      name: "🎯 **Category Examples**",
      value: "• **Items** 🎒 - General items and resources\n" +
             "• **Dinosaurs** 🦕 - Tamed creatures\n" +
             "• **Eggs** 🥚 - Fertilized eggs for breeding\n" +
             "• **Blueprints** 📋 - Item blueprints and schematics\n" +
             "• **Boss Fights** ⚔️ - Boss fight services\n" +
             "• **Base Spots** 🏠 - Base locations and spots\n" +
             "• **XP Parties** 🎉 - Experience farming services\n" +
             "• **Other** 📦 - Miscellaneous items\n" +
             "• **Bundles** 🎁 - Package deals",
      inline: false
    },
    {
      name: "🔒 **Security Benefits of Thread Mode**",
      value: "• **Role Separation**: Boss sellers can't access item threads\n" +
             "• **Organized Trading**: Each category has its own space\n" +
             "• **Reduced Confusion**: Users only see relevant categories\n" +
             "• **Better Moderation**: Easier to track category-specific activity",
      inline: false
    },
    {
      name: "⚠️ **Important Notes**",
      value: "• **Existing carts** continue working in their current mode\n" +
             "• **New carts** will use the configured mode\n" +
             "• **Thread mode requires** category permission setup\n" +
             "• **Can switch modes** anytime without data loss",
      inline: false
    }
  );

  await interaction.editReply({ embeds: [embed] });
}
