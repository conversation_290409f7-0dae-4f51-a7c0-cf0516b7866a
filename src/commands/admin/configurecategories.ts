import {
  ApplicationCommandOptionType,
  ApplicationCommandType,
  CommandInteraction,
  EmbedBuilder,
  Client
} from "discord.js";
import { Command } from "../../types";
import { GuildCartHelper, ItemCategory } from "../../utils/guildCartHelper";

const CATEGORY_CHOICES = [
  { name: "Items 🎒", value: "item" },
  { name: "Dinosaurs 🦕", value: "dino" },
  { name: "Eggs 🥚", value: "egg" },
  { name: "Blueprints 📋", value: "blueprint" },
  { name: "Boss Fights ⚔️", value: "boss_fight" },
  { name: "Base Spots 🏠", value: "base_spot" },
  { name: "XP Parties 🎉", value: "xp_party" },
  { name: "Other 📦", value: "other" },
  { name: "Bundles 🎁", value: "bundle" }
];

export const configurecategories: Command = {
  name: "configurecategories",
  description: "Configure category permissions for thread-based cart system (Guild Owner Only)",
  category: "admin",
  type: ApplicationCommandType.ChatInput,
  options: [
    {
      name: "action",
      description: "What action to perform",
      type: ApplicationCommandOptionType.String,
      required: true,
      choices: [
        { name: "View Current Settings", value: "view" },
        { name: "Show Help & Info", value: "help" },
        { name: "Add Category Role", value: "add" },
        { name: "Remove Category Role", value: "remove" },
        { name: "Clear Category Roles", value: "clear" },
        { name: "Clear All Categories", value: "clear_all" }
      ]
    },
    {
      name: "category",
      description: "The category to configure (required for add/remove/clear actions)",
      type: ApplicationCommandOptionType.String,
      required: false,
      choices: CATEGORY_CHOICES
    },
    {
      name: "role",
      description: "The role to add/remove (required for add/remove actions)",
      type: ApplicationCommandOptionType.Role,
      required: false
    }
  ],

  run: async (_client: Client, interaction: CommandInteraction) => {
    // Check if user is guild owner
    if (!interaction.guild || interaction.user.id !== interaction.guild.ownerId) {
      await interaction.reply({
        content: "❌ Only the server owner can configure category permissions.",
        ephemeral: true
      });
      return;
    }

    await interaction.deferReply({ ephemeral: true });

    const action = interaction.options.get("action", true).value as string;
    const category = interaction.options.get("category")?.value as ItemCategory;
    const role = interaction.options.get("role")?.role;

    try {
      switch (action) {
        case "view":
          await handleViewSettings(interaction);
          break;
        case "help":
          await handleShowHelp(interaction);
          break;
        case "add":
          if (!category || !role) {
            await interaction.editReply({ 
              content: "❌ Please specify both a category and a role to add." 
            });
            return;
          }
          await handleAddCategoryRole(interaction, category, role.id, role.name);
          break;
        case "remove":
          if (!category || !role) {
            await interaction.editReply({ 
              content: "❌ Please specify both a category and a role to remove." 
            });
            return;
          }
          await handleRemoveCategoryRole(interaction, category, role.id, role.name);
          break;
        case "clear":
          if (!category) {
            await interaction.editReply({ 
              content: "❌ Please specify a category to clear." 
            });
            return;
          }
          await handleClearCategory(interaction, category);
          break;
        case "clear_all":
          await handleClearAllCategories(interaction);
          break;
        default:
          await interaction.editReply({
            content: "❌ Unknown action."
          });
      }
    } catch (error) {
      console.error("Error in configurecategories command:", error);
      await interaction.editReply({
        content: "❌ An error occurred while configuring category permissions."
      });
    }
  }
};

async function handleViewSettings(interaction: CommandInteraction) {
  if (!interaction.guild) return;

  const cartMode = await GuildCartHelper.getGuildCartMode(interaction.guild.id);
  const permissions = await GuildCartHelper.getAllCategoryPermissions(interaction.guild.id);

  const embed = new EmbedBuilder()
    .setTitle("🧵 Category Permissions Configuration")
    .setDescription("Current category permission settings for thread-based cart system")
    .setColor(0x3498db)
    .setTimestamp();

  if (cartMode !== 'threads') {
    embed.addFields({
      name: "⚠️ Thread Mode Not Active",
      value: "Category permissions only apply when **Thread Cart Mode** is enabled.\n" +
             "Use `/configurecart action:Set Thread Cart Mode` to enable thread-based carts.",
      inline: false
    });
  }

  // Add category permission fields
  for (const [category, roleIds] of Object.entries(permissions)) {
    const categoryChoice = CATEGORY_CHOICES.find(c => c.value === category);
    const categoryName = categoryChoice ? categoryChoice.name : category;
    
    let rolesText = "No roles configured (everyone has access)";
    if (roleIds.length > 0) {
      const roleNames = roleIds.map(roleId => {
        const role = interaction.guild!.roles.cache.get(roleId);
        return role ? `<@&${roleId}>` : `Unknown Role (${roleId})`;
      });
      rolesText = roleNames.join(", ");
    }

    embed.addFields({
      name: categoryName,
      value: rolesText,
      inline: true
    });
  }

  embed.addFields({
    name: "ℹ️ Quick Info",
    value: "• **No roles configured** = Everyone has access to that category\n" +
           "• **Roles configured** = Only those roles can access the category thread\n" +
           "• Use `/configurecategories action:Show Help & Info` for detailed explanations",
    inline: false
  });

  await interaction.editReply({ embeds: [embed] });
}

async function handleAddCategoryRole(
  interaction: CommandInteraction, 
  category: ItemCategory, 
  roleId: string, 
  roleName: string
) {
  if (!interaction.guild) return;

  const success = await GuildCartHelper.addCategoryRole(interaction.guild.id, category, roleId);

  if (success) {
    const categoryChoice = CATEGORY_CHOICES.find(c => c.value === category);
    const categoryName = categoryChoice ? categoryChoice.name : category;
    
    await interaction.editReply({
      content: `✅ Successfully added **${roleName}** to **${categoryName}** category permissions.`
    });
  } else {
    await interaction.editReply({
      content: `❌ Failed to add role. It may already be configured for this category.`
    });
  }
}

async function handleRemoveCategoryRole(
  interaction: CommandInteraction, 
  category: ItemCategory, 
  roleId: string, 
  roleName: string
) {
  if (!interaction.guild) return;

  const success = await GuildCartHelper.removeCategoryRole(interaction.guild.id, category, roleId);

  if (success) {
    const categoryChoice = CATEGORY_CHOICES.find(c => c.value === category);
    const categoryName = categoryChoice ? categoryChoice.name : category;
    
    await interaction.editReply({
      content: `✅ Successfully removed **${roleName}** from **${categoryName}** category permissions.`
    });
  } else {
    await interaction.editReply({
      content: `❌ Failed to remove role. It may not be configured for this category.`
    });
  }
}

async function handleClearCategory(interaction: CommandInteraction, category: ItemCategory) {
  if (!interaction.guild) return;

  const success = await GuildCartHelper.clearCategoryRoles(interaction.guild.id, category);

  if (success) {
    const categoryChoice = CATEGORY_CHOICES.find(c => c.value === category);
    const categoryName = categoryChoice ? categoryChoice.name : category;
    
    await interaction.editReply({
      content: `✅ Successfully cleared all roles from **${categoryName}** category. Everyone now has access.`
    });
  } else {
    await interaction.editReply({
      content: `❌ Failed to clear category roles.`
    });
  }
}

async function handleClearAllCategories(interaction: CommandInteraction) {
  if (!interaction.guild) return;

  const success = await GuildCartHelper.clearAllCategoryPermissions(interaction.guild.id);

  if (success) {
    await interaction.editReply({
      content: `✅ Successfully cleared all category permissions. Everyone now has access to all categories.`
    });
  } else {
    await interaction.editReply({
      content: `❌ Failed to clear all category permissions.`
    });
  }
}

async function handleShowHelp(interaction: CommandInteraction) {
  const embed = new EmbedBuilder()
    .setTitle("🧵 Category Permissions Help")
    .setDescription("Complete guide to configuring category-specific permissions for thread-based cart system")
    .setColor(0x2ecc71)
    .setTimestamp();

  embed.addFields(
    {
      name: "🎯 **What Are Category Permissions?**",
      value: "Category permissions control which roles can access specific category threads in the thread-based cart system.\n" +
             "• **Thread Mode Required**: Only works when Thread Cart Mode is enabled\n" +
             "• **Security Feature**: Prevents unauthorized cross-category selling\n" +
             "• **Role-Based Access**: Different roles see different category threads",
      inline: false
    },
    {
      name: "🔒 **How It Works**",
      value: "**No Roles Configured** = Everyone has access to that category\n" +
             "**Roles Configured** = Only those roles can see/access the category thread\n\n" +
             "**Example**: If you add `@Boss-Seller` to Boss Fights category:\n" +
             "• Only users with `@Boss-Seller` role can see the Boss Fights thread\n" +
             "• Other users won't see that thread in cart channels",
      inline: false
    },
    {
      name: "📋 **Available Categories**",
      value: "🎒 **Items** - General items and resources\n" +
             "🦕 **Dinosaurs** - Tamed creatures\n" +
             "🥚 **Eggs** - Fertilized eggs for breeding\n" +
             "📋 **Blueprints** - Item blueprints and schematics\n" +
             "⚔️ **Boss Fights** - Boss fight services\n" +
             "🏠 **Base Spots** - Base locations and spots\n" +
             "🎉 **XP Parties** - Experience farming services\n" +
             "📦 **Other** - Miscellaneous items\n" +
             "🎁 **Bundles** - Package deals",
      inline: false
    },
    {
      name: "🎯 **Use Case Examples**",
      value: "**Specialized Sellers:**\n" +
             "• `@Dino-Breeder` → Only access Dinosaurs & Eggs threads\n" +
             "• `@Boss-Carry` → Only access Boss Fights thread\n" +
             "• `@Item-Seller` → Only access Items & Blueprints threads\n\n" +
             "**Security Benefits:**\n" +
             "• Prevents dino breeders from accidentally selling items\n" +
             "• Keeps boss fight services separate from trading\n" +
             "• Organized, role-specific access control",
      inline: false
    },
    {
      name: "⚙️ **Setup Process**",
      value: "1. **Enable Thread Mode**: `/configurecart action:Set Thread Cart Mode`\n" +
             "2. **Add Category Roles**: `/configurecategories action:Add Category role:@Role category:Items`\n" +
             "3. **Verify Settings**: `/configurecategories action:View Current Settings`\n" +
             "4. **Test**: Create a new cart to see category threads in action",
      inline: false
    },
    {
      name: "📋 **Available Actions Explained**",
      value: "**View Current Settings** - Shows all category permissions\n" +
             "**Show Help & Info** - Shows this detailed help guide\n" +
             "**Add Category Role** - Adds a role to a specific category\n" +
             "**Remove Category Role** - Removes a role from a specific category\n" +
             "**Clear Category Roles** - Removes all roles from one category\n" +
             "**Clear All Categories** - Removes all category permissions (reset)",
      inline: false
    },
    {
      name: "⚠️ **Important Notes**",
      value: "• **Thread Mode Required**: Category permissions only work in Thread Cart Mode\n" +
             "• **Backward Compatibility**: Single Cart Mode ignores category permissions\n" +
             "• **Default Access**: Categories with no roles = everyone has access\n" +
             "• **Immediate Effect**: Changes apply to new cart channels instantly\n" +
             "• **Guild Owner Only**: Only server owner can configure permissions",
      inline: false
    },
    {
      name: "💡 **Quick Examples**",
      value: "**Add a role to Items category:**\n" +
             "`/configurecategories action:Add Category category:Items role:@Item-Seller`\n\n" +
             "**Remove a role from Boss Fights:**\n" +
             "`/configurecategories action:Remove Category category:Boss Fights role:@Old-Boss-Seller`\n\n" +
             "**Clear all roles from Eggs category:**\n" +
             "`/configurecategories action:Clear Category category:Eggs`",
      inline: false
    }
  );

  await interaction.editReply({ embeds: [embed] });
}
