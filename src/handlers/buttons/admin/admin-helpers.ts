import { ButtonInteraction } from "discord.js";
import { BrandingManager } from "../../../services/branding-manager";
import { TextToggleManager } from "../../../services/text-toggle-manager";
import { EmbedUpdateService } from "../../../services/embed-update-service";

/**
 * Shared helper functions for admin operations
 */
export class AdminHelpers {
    /**
     * Find a listing message in the channel
     */
    static async findListingMessage(
        interaction: ButtonInteraction,
        listingId: string,
        limit: number = 50
    ): Promise<any> {
        const channel = interaction.channel;
        if (!channel || !('messages' in channel)) {
            return null;
        }

        const messages = await channel.messages.fetch({ limit });
        return messages.find(msg => {
            if (msg.author.id !== interaction.client.user?.id || msg.embeds.length === 0) {
                return false;
            }
            const embed = msg.embeds[0];
            return embed?.title?.includes(`#${listingId}`) ||
                   msg.components?.some((row: any) =>
                       row.components?.some((component: any) =>
                           component.customId === `admin_manage_${listingId}`
                       )
                   );
        });
    }

    /**
     * Extract listing ID from custom ID
     */
    static extractListingIdFromCustomId(customId: string): string {
        const parts = customId.split('_');
        return parts[parts.length - 1] || '';
    }

    /**
     * Handle branding toggle logic
     */
    static async handleBrandingToggle(
        interaction: ButtonInteraction,
        listingId: string,
        toggleType: 'branding' | 'link'
    ): Promise<{ success: boolean; statusText: string }> {
        const listingMessage = await this.findListingMessage(interaction, listingId);
        if (!listingMessage) {
            return { success: false, statusText: '' };
        }

        const guildId = interaction.guild?.id || '';
        const channelId = interaction.channel?.id || '';

        // Toggle the appropriate branding type
        let isDisabled: boolean;
        if (toggleType === 'branding') {
            const currentState = BrandingManager.isBrandingDisabled(guildId, channelId, listingMessage.id);
            if (currentState) {
                BrandingManager.enableBranding(guildId, channelId, listingMessage.id);
                isDisabled = false;
            } else {
                BrandingManager.disableBranding(guildId, channelId, listingMessage.id);
                isDisabled = true;
            }
        } else {
            const currentState = BrandingManager.isLinkDisabled(guildId, channelId, listingMessage.id);
            if (currentState) {
                BrandingManager.enableLink(guildId, channelId, listingMessage.id);
                isDisabled = false;
            } else {
                BrandingManager.disableLink(guildId, channelId, listingMessage.id);
                isDisabled = true;
            }
        }

        // Update the embed
        await this.updateEmbedWithBranding(listingMessage, guildId, channelId, listingId);

        const statusText = isDisabled ? 'disabled' : 'enabled';
        return { success: true, statusText };
    }

    /**
     * Handle text toggle logic
     */
    static async handleTextToggle(
        interaction: ButtonInteraction,
        listingId: string,
        toggleType: 'cluster' | 'type' | 'category' | 'description'
    ): Promise<{ success: boolean; statusText: string }> {
        const listingMessage = await this.findListingMessage(interaction, listingId);
        if (!listingMessage) {
            return { success: false, statusText: '' };
        }

        const guildId = interaction.guild?.id || '';
        const channelId = interaction.channel?.id || '';

        // Toggle the appropriate text type
        let isDisabled: boolean;
        switch (toggleType) {
            case 'cluster':
                isDisabled = TextToggleManager.toggleClusterText(guildId, channelId, listingMessage.id);
                break;
            case 'type':
                isDisabled = TextToggleManager.toggleTypeText(guildId, channelId, listingMessage.id);
                break;
            case 'category':
                isDisabled = TextToggleManager.toggleCategoryText(guildId, channelId, listingMessage.id);
                break;
            case 'description':
                isDisabled = TextToggleManager.toggleDescriptionText(guildId, channelId, listingMessage.id);
                break;
            default:
                return { success: false, statusText: '' };
        }

        // Update the embed
        await this.updateEmbedWithTextToggles(listingMessage, guildId, channelId, listingId);

        const statusText = isDisabled ? 'hidden' : 'shown';
        return { success: true, statusText };
    }

    /**
     * Update embed with branding changes
     */
    private static async updateEmbedWithBranding(
        listingMessage: any,
        guildId: string,
        channelId: string,
        listingId: string
    ): Promise<void> {
        const originalEmbed = listingMessage.embeds[0];
        const updatedEmbed = EmbedUpdateService.updateEmbedWithBranding(
            originalEmbed,
            guildId,
            channelId,
            listingMessage.id,
            listingId
        );

        const editOptions = await this.prepareEditOptions(
            listingMessage,
            updatedEmbed,
            originalEmbed,
            listingId
        );

        await listingMessage.edit(editOptions);
    }

    /**
     * Update embed with text toggle changes
     */
    private static async updateEmbedWithTextToggles(
        listingMessage: any,
        guildId: string,
        channelId: string,
        listingId: string
    ): Promise<void> {
        const originalEmbed = listingMessage.embeds[0];
        const updatedEmbed = await EmbedUpdateService.updateEmbedWithTextToggles(
            originalEmbed,
            guildId,
            channelId,
            listingMessage.id,
            listingId
        );

        const editOptions = await this.prepareEditOptions(
            listingMessage,
            updatedEmbed,
            originalEmbed,
            listingId
        );

        await listingMessage.edit(editOptions);
    }

    /**
     * Prepare edit options for message update
     */
    private static async prepareEditOptions(
        listingMessage: any,
        updatedEmbed: any,
        originalEmbed: any,
        listingId: string
    ): Promise<any> {
        const editOptions: any = {
            embeds: [updatedEmbed],
            components: listingMessage.components
        };

        // Check if the embed uses attachment URLs
        const hasAttachmentImage = originalEmbed.image?.url?.startsWith('attachment://');
        const hasAttachmentThumbnail = originalEmbed.thumbnail?.url?.startsWith('attachment://');

        if (hasAttachmentImage || hasAttachmentThumbnail) {
            try {
                // Need to regenerate the attachment for the listing
                const { ImageService } = await import('../../../services/image-service.js');
                const regeneratedAttachment = await ImageService.createListingPreviewImage(parseInt(listingId));

                if (regeneratedAttachment) {
                    editOptions.files = [regeneratedAttachment];
                } else {
                    // If regeneration fails, clear the attachment URL and use no image
                    if (hasAttachmentImage) {
                        updatedEmbed.setImage(null);
                    }
                    if (hasAttachmentThumbnail) {
                        updatedEmbed.setThumbnail(null);
                    }
                    editOptions.files = [];
                }
            } catch (error) {
                console.error('Error regenerating attachment:', error);
                editOptions.files = [];
            }
        } else {
            editOptions.files = [];
            editOptions.attachments = [];
        }

        return editOptions;
    }
}
