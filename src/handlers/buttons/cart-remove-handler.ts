import { ButtonInteraction, TextChannel, ThreadChannel } from "discord.js";
import { cartManager } from "../../utils/cartManager";
import { CartSummaryService } from "../../services/cart-summary-service";
import { NumberFormatter } from "../../utils/numberFormatter";
import { ItemCategory } from "../../utils/guildCartHelper";

/**
 * Handle cart item removal button interactions
 */
export async function handleCartRemoveButton(
    interaction: ButtonInteraction,
    itemId: string
): Promise<void> {
    try {
        await interaction.deferReply({ ephemeral: true });

        const userId = interaction.user.id;
        const guildId = interaction.guildId;

        if (!guildId) {
            await interaction.followUp({
                content: 'This command can only be used in a server.',
                ephemeral: true
            });
            return;
        }

        // Get the current cart
        const cart = cartManager.getCart(userId, guildId);
        if (!cart || cart.items.length === 0) {
            await interaction.followUp({
                content: 'Your cart is already empty.',
                ephemeral: true
            });
            return;
        }

        // Find the item to remove by recreating the same ID
        let itemToRemove = null;
        let itemIndex = -1;

        console.log(`[Cart Remove] Looking for item with ID: ${itemId}`);
        console.log(`[Cart Remove] Cart has ${cart.items.length} items`);

        for (let i = 0; i < cart.items.length; i++) {
            const item = cart.items[i];
            const generatedId = generateItemId(
                item.title,
                item.quantity,
                item.paymentMethod || '',
                item.listingType
            );

            console.log(`[Cart Remove] Item ${i}: "${item.title}" x${item.quantity} (${item.paymentMethod}) [${item.listingType}] -> ID: ${generatedId}`);

            if (generatedId === itemId) {
                itemToRemove = item;
                itemIndex = i;
                console.log(`[Cart Remove] Found matching item at index ${i}`);
                break;
            }

            // Also try generating ID with quantity 1 (for consolidated items)
            const alternativeId = generateItemId(
                item.title,
                1, // Try with quantity 1
                item.paymentMethod || '',
                item.listingType
            );

            if (alternativeId === itemId) {
                itemToRemove = item;
                itemIndex = i;
                console.log(`[Cart Remove] Found matching item using alternative ID (quantity 1) at index ${i}`);
                break;
            }
        }

        // If primary ID matching failed, try fallback methods
        if (!itemToRemove || itemIndex === -1) {
            console.log(`[Cart Remove] Primary ID matching failed, trying fallback methods`);

            // Try to find by button message context (get the embed from the message that was clicked)
            if (interaction.message && interaction.message.embeds.length > 0) {
                const embed = interaction.message.embeds[0];
                const fallbackItem = parseItemFromEmbed(embed);

                if (fallbackItem) {
                    console.log(`[Cart Remove] Parsed fallback item: "${fallbackItem.title}" x${fallbackItem.quantity} (${fallbackItem.paymentMethod}) [${fallbackItem.listingType}]`);

                    // Try to find by matching title, payment method, and listing type (ignore quantity for consolidated items)
                    for (let i = 0; i < cart.items.length; i++) {
                        const item = cart.items[i];
                        if (item.title === fallbackItem.title &&
                            item.paymentMethod === fallbackItem.paymentMethod &&
                            item.listingType === fallbackItem.listingType) {
                            itemToRemove = item;
                            itemIndex = i;
                            console.log(`[Cart Remove] Found consolidated item using fallback method at index ${i} (cart quantity: ${item.quantity}, message quantity: ${fallbackItem.quantity})`);
                            break;
                        }
                    }
                }
            }
        }

        if (!itemToRemove || itemIndex === -1) {
            console.log(`[Cart Remove] Could not find item to remove with any method`);
            await interaction.followUp({
                content: 'Could not find the item to remove. It may have already been removed.',
                ephemeral: true
            });
            return;
        }

        // Determine how much quantity to remove from this button click
        let quantityToRemove = 1; // Default: remove 1 unit

        // If we parsed the item from the message embed, use that quantity
        if (interaction.message && interaction.message.embeds.length > 0) {
            const embed = interaction.message.embeds[0];
            const messageItem = parseItemFromEmbed(embed);
            if (messageItem) {
                quantityToRemove = messageItem.quantity;
                console.log(`[Cart Remove] Will remove ${quantityToRemove} units from cart item with ${itemToRemove.quantity} units`);
            }
        }

        // Handle quantity reduction vs complete removal
        if (itemToRemove.quantity > quantityToRemove) {
            // Reduce quantity instead of removing completely
            cart.items[itemIndex].quantity -= quantityToRemove;
            cart.updatedAt = new Date();
            console.log(`[Cart Remove] Reduced quantity from ${itemToRemove.quantity + quantityToRemove} to ${cart.items[itemIndex].quantity}`);
        } else {
            // Remove the entire item if quantity would be 0 or less
            cart.items.splice(itemIndex, 1);
            console.log(`[Cart Remove] Removed entire item from cart`);
        }

        // Delete the message containing this item
        try {
            await interaction.message?.delete();
        } catch (error) {
            console.error('Error deleting item message:', error);
            // Continue even if message deletion fails
        }

        // Update the appropriate summary (main channel or thread)
        const channel = interaction.channel;
        if (channel) {
            if (channel.isThread()) {
                // We're in a thread - update thread summary
                const threadChannel = channel as ThreadChannel;

                // Determine category from the removed item
                const category = (itemToRemove.category || 'other') as ItemCategory;

                await CartSummaryService.updateThreadSummary(
                    threadChannel,
                    userId,
                    guildId,
                    interaction.user.toString(),
                    category
                );
            } else {
                // We're in main channel - update main summary
                const textChannel = channel as TextChannel;
                await CartSummaryService.updateCartSummary(
                    textChannel,
                    userId,
                    guildId,
                    interaction.user.toString()
                );
            }
        }

        // Notify the user
        const formattedQuantity = NumberFormatter.formatWithCommas(quantityToRemove);
        const remainingQuantity = cart.items[itemIndex]?.quantity || 0;
        const remainingText = remainingQuantity > 0 ? ` (${NumberFormatter.formatWithCommas(remainingQuantity)} remaining)` : '';

        await interaction.followUp({
            content: `✅ Removed **${itemToRemove.title}** x${formattedQuantity} from your cart${remainingText}.`,
            ephemeral: true
        });

    } catch (error) {
        console.error('Error handling cart remove button:', error);
        await interaction.followUp({
            content: 'An error occurred while removing the item from your cart.',
            ephemeral: true
        });
    }
}

/**
 * Generate the same item ID as used in cart-summary-service.ts
 * Normalized to handle formatting inconsistencies
 */
function generateItemId(
    title: string,
    quantity: number,
    paymentMethod: string,
    listingType?: 'buy' | 'sell'
): string {
    // Normalize the data to handle formatting inconsistencies
    const normalizedTitle = title.trim().replace(/\*\*/g, ''); // Remove markdown formatting
    const normalizedPayment = paymentMethod.trim();
    const normalizedType = listingType || 'unknown';

    // Create a unique hash based on normalized item properties
    const data = `${normalizedTitle}_${quantity}_${normalizedPayment}_${normalizedType}`;
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
        const char = data.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString();
}

/**
 * Parse item data from a Discord embed (fallback method)
 */
function parseItemFromEmbed(embed: any): { title: string; quantity: number; paymentMethod: string; listingType?: 'buy' | 'sell' } | null {
    try {
        const fields = embed.fields;
        if (!fields || fields.length < 3) return null;

        // Look for field patterns
        let buyingField = fields.find((f: any) => f.name.includes('🛒 Buying'));
        let sellingField = fields.find((f: any) => f.name.includes('💰 Selling'));
        const quantityField = fields.find((f: any) => f.name === '🔢 Quantity');
        let receiveField = fields.find((f: any) => f.name.includes('💰 Wants to Receive'));
        let willPayField = fields.find((f: any) => f.name.includes('💳 Will Pay'));

        // Determine listing type and extract data
        let title: string;
        let listingType: 'buy' | 'sell' | undefined;
        let paymentMethod: string;

        if (buyingField) {
            // User is buying from a selling listing
            title = buyingField.value.replace(/\*\*/g, '').trim();
            listingType = 'sell';
            paymentMethod = willPayField?.value?.trim() || 'Unknown payment';
        } else if (sellingField) {
            // User is selling to a buying listing
            title = sellingField.value.replace(/\*\*/g, '').trim();
            listingType = 'buy';
            paymentMethod = receiveField?.value?.trim() || 'Unknown payment';
        } else {
            return null;
        }

        if (!quantityField) return null;

        // Parse quantity, removing any commas
        const quantityStr = quantityField.value.replace(/,/g, '');
        const quantity = parseInt(quantityStr);

        if (isNaN(quantity)) return null;

        return {
            title,
            quantity,
            paymentMethod,
            listingType
        };
    } catch (error) {
        console.error('Error parsing item from embed:', error);
        return null;
    }
}
