import { ButtonInteraction, TextChannel, GuildMember } from "discord.js";
import { ChannelService } from "../../services/channel-service";
import { TranscriptService } from "../../services/transcript-service";
import { TicketService } from "../../services/ticket-service";

/**
 * Handle ticket-related button actions
 */
export async function handleTicketButton(
    interaction: ButtonInteraction,
    subAction: string,
    _id?: string
): Promise<void> {
    await interaction.deferUpdate();

    switch (subAction) {
        case 'transcript':
            await handleTranscript(interaction);
            break;
        case 'close':
            await handleCloseTicket(interaction);
            break;
        default:
            await interaction.followUp({
                content: 'Unknown ticket action.',
                ephemeral: true
            });
    }
}

/**
 * Handle transcript generation (admin only)
 */
async function handleTranscript(interaction: ButtonInteraction): Promise<void> {
    try {
        // Check admin permissions
        if (!interaction.member || !interaction.guild) {
            await interaction.followUp({
                content: 'This action can only be used in a server.',
                ephemeral: true
            });
            return;
        }

        const member = interaction.member;
        if (!(member instanceof GuildMember)) {
            await interaction.followUp({
                content: 'Unable to verify permissions.',
                ephemeral: true
            });
            return;
        }

        // Check if user can save transcripts (admin or ticket access)
        const canSaveTranscripts = await TicketService.canSaveTranscripts(member);
        if (!canSaveTranscripts) {
            await interaction.followUp({
                content: '❌ You do not have permission to save transcripts. Only administrators or users with ticket access can perform this action.',
                ephemeral: true
            });
            return;
        }

        if (!interaction.channel || !('send' in interaction.channel)) {
            await interaction.followUp({
                content: 'Cannot locate the channel to save transcript from.',
                ephemeral: true
            });
            return;
        }

        // Show loading message
        await interaction.followUp({
            content: '📝 Generating transcript... This may take a moment.',
            ephemeral: true
        });

        // Save the transcript
        const success = await TranscriptService.saveTicketTranscript(
            interaction.guild,
            interaction.channel as TextChannel,
            interaction.user
        );

        if (success) {
            await interaction.followUp({
                content: '✅ Transcript saved successfully! Check the admin-transcripts channel for the full transcript.',
                ephemeral: true
            });
        } else {
            await interaction.followUp({
                content: '❌ Failed to save transcript. Please try again or contact an administrator.',
                ephemeral: true
            });
        }
    } catch (error) {
        console.error('Error saving transcript:', error);
        await interaction.followUp({
            content: 'An error occurred while saving the transcript.',
            ephemeral: true
        });
    }
}

/**
 * Handle closing a ticket (admin only)
 */
async function handleCloseTicket(interaction: ButtonInteraction): Promise<void> {
    try {
        // Check admin permissions
        if (!interaction.member || !interaction.guild) {
            await interaction.followUp({
                content: 'This action can only be used in a server.',
                ephemeral: true
            });
            return;
        }

        const member = interaction.member;
        if (!(member instanceof GuildMember)) {
            await interaction.followUp({
                content: 'Unable to verify permissions.',
                ephemeral: true
            });
            return;
        }

        // Check if user can close tickets (admin or ticket access)
        const canCloseTickets = await TicketService.canCloseTickets(member);
        if (!canCloseTickets) {
            await interaction.followUp({
                content: '❌ You do not have permission to close tickets. Only administrators or users with ticket access can perform this action.',
                ephemeral: true
            });
            return;
        }

        if (!interaction.channel || !('send' in interaction.channel)) {
            await interaction.followUp({
                content: 'Cannot locate the channel to close.',
                ephemeral: true
            });
            return;
        }

        // Close the ticket channel using the service with automatic transcript saving
        await ChannelService.closeTicketChannel(interaction.channel as TextChannel, interaction.user);
    } catch (error) {
        console.error('Error closing ticket:', error);
        await interaction.followUp({
            content: 'An error occurred while closing the ticket.',
            ephemeral: true
        });
    }
}
