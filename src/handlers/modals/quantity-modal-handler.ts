import {
    ModalSubmitInteraction
} from "discord.js";
import { cartManager } from "../../utils/cartManager";
import { ChannelService } from "../../services/channel-service";
import { CartSummaryService } from "../../services/cart-summary-service";
import { CartThreadService } from "../../services/cart-thread-service";
import { EmojiService } from "../../services/emoji-service";
import { NumberFormatter } from "../../utils/numberFormatter";
import pool from "../../utils/dbConfig";
import { GuildRoleHelper } from "../../utils/guildRoleHelper";
import { GuildCartHelper } from "../../utils/guildCartHelper";

/**
 * Handle cart quantity modal submission
 */
export async function handleQuantityModal(
    interaction: ModalSubmitInteraction,
    listingId: string,
    paymentId: string
): Promise<void> {
    await interaction.deferUpdate();

    try {
        const quantity = parseInt(interaction.fields.getTextInputValue('quantity') || '1');

        if (isNaN(quantity) || quantity < 1) {
            await interaction.followUp({
                content: 'Please enter a valid quantity (minimum 1).',
                ephemeral: true
            });
            return;
        }

        const userId = interaction.user.id;
        const guildId = interaction.guildId || 'DM';

        // Fetch listing details
        const listingQuery = `
            SELECT l.*
            FROM asa.listings l
            WHERE l.listing_id = $1
        `;

        const listingResult = await pool.query(listingQuery, [listingId]);

        if (listingResult.rows.length === 0) {
            await interaction.followUp({
                content: 'This listing no longer exists.',
                ephemeral: true
            });
            return;
        }

        const listing = listingResult.rows[0];

        // Fetch payment details if paymentId is provided and not 'contact'
        let paymentMethod = 'Contact seller for details';
        let paymentIdNumber: number | undefined = undefined;

        if (paymentId !== 'contact') {
            try {
                const paymentIdAsNumber = parseInt(paymentId);

                const paymentQuery = `
                    SELECT p.*, i.name as item_name
                    FROM asa.payment_options p
                    LEFT JOIN asa.items i ON SPLIT_PART(p.item_id, ' ', 1) = SPLIT_PART(i.id, ' ', 1)
                    WHERE p.payment_id = $1
                `;

                const paymentResult = await pool.query(paymentQuery, [paymentIdAsNumber]);

                if (paymentResult.rows.length > 0) {
                    const paymentData = paymentResult.rows[0];
                    paymentIdNumber = paymentIdAsNumber;

                    if (paymentData.item_id) {
                        const itemName = paymentData.item_name || 'Item';
                        // Use icon_name from database if available, otherwise fall back to item name
                        const iconIdentifier = paymentData.icon_name || itemName;
                        const emoji = EmojiService.getItemEmojiByIconName(iconIdentifier);
                        const formattedQuantity = NumberFormatter.formatWithCommas(paymentData.quantity || 1);
                        paymentMethod = `${emoji} ${itemName} x${formattedQuantity}`;
                    } else if (paymentData.custom_price) {
                        const emoji = EmojiService.getCurrencyEmoji(paymentData.custom_price);
                        paymentMethod = `${emoji} ${paymentData.custom_price}`;
                    } else if (paymentData.contact_for_price) {
                        paymentMethod = '📞 Contact for price';
                    }
                }
            } catch (err) {
                console.error('Error parsing payment ID or querying database:', err);
            }
        }

        // Add to cart with quantity
        cartManager.addItem(userId, guildId, {
            listingId: parseInt(listingId),
            title: listing.title || `Listing #${listingId}`,
            quantity: quantity,
            paymentMethod,
            paymentId: paymentIdNumber,
            listingType: listing.listing_type, // Include the listing type
            category: listing.category || 'other'
        });

        // Get or create cart channel (ensures only one cart channel per user)
        if (interaction.guild) {
            const { channel, isNew: isNewChannel } = await ChannelService.getOrCreateCartChannel(
                interaction.guild,
                interaction.client,
                interaction.user.id,
                interaction.user.username
            );

            if (!channel) {
                await interaction.followUp({
                    content: 'Error: Could not create or access cart channel.',
                    ephemeral: true
                });
                return;
            }

            if (isNewChannel) {
                // Get cart notify roles to tag
                const notifyRoles = await GuildRoleHelper.getGuildRoles(interaction.guild.id, 'cart_notify');

                // Create notification content
                let notificationContent = `🛒 **Cart Channel Created**\n${interaction.user}, this is your personal cart channel! All your trades will be managed here.`;

                // Add role notifications if any are configured
                if (notifyRoles.length > 0) {
                    const roleMentions = notifyRoles.map(roleId => `<@&${roleId}>`).join(' ');
                    notificationContent += `\n\n🔔 **New Customer Alert:** ${roleMentions}`;
                }

                // Send initial message with user tag for identification - this MUST be the first message
                await channel.send({
                    content: notificationContent
                });

                // Check if we need to create category threads
                const cartMode = await GuildCartHelper.getGuildCartMode(interaction.guild.id);
                if (cartMode === 'threads') {
                    const userMember = await interaction.guild.members.fetch(interaction.user.id);
                    await CartThreadService.createCategoryThreads(channel, interaction.user.id, userMember);
                }
            }

            // Determine where to post the item (main channel or appropriate thread)
            const category = listing.category || 'other';
            const userMember = await interaction.guild.members.fetch(interaction.user.id);
            const targetChannel = await CartThreadService.getThreadForItem(
                channel,
                category,
                interaction.user.id,
                userMember
            );

            // Add individual item message
            const itemData = CartSummaryService.createItemEmbed(
                listing.title || `Listing #${listingId}`,
                quantity,
                paymentMethod,
                interaction.user.toString(),
                listing.listing_type
            );

            await targetChannel.send({
                embeds: [itemData.embed],
                components: itemData.components
            });

            // Update the cart summary at the end of the channel
            await CartSummaryService.updateCartSummary(
                channel,
                userId,
                guildId,
                interaction.user.toString()
            );

            // Notify the user
            const notificationMessage = isNewChannel
                ? `Item added to cart! Your cart channel has been created in ${channel}.`
                : `Item added to your existing cart channel ${channel}.`;

            await interaction.followUp({
                content: notificationMessage,
                ephemeral: true
            });
        } else {
            // If not in a guild, just notify that the item was added
            const formattedQuantity = NumberFormatter.formatWithCommas(quantity);
            await interaction.followUp({
                content: `${listing.title || `Listing #${listingId}`} x${formattedQuantity} added to your cart with payment: ${paymentMethod}`,
                ephemeral: true
            });
        }
    } catch (error) {
        console.error('Error handling quantity modal submission:', error);
        await interaction.followUp({
            content: 'An error occurred while processing your request.',
            ephemeral: true
        });
    }
}
