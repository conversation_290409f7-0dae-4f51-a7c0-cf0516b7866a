import {
    ModalSubmitInteraction
} from "discord.js";
import { cartManager } from "../../utils/cartManager";
import { ChannelService } from "../../services/channel-service";
import { CartSummaryService } from "../../services/cart-summary-service";
import { CartThreadService } from "../../services/cart-thread-service";
import { NumberFormatter } from "../../utils/numberFormatter";
import pool from "../../utils/dbConfig";
import { GuildRoleHelper } from "../../utils/guildRoleHelper";
import { GuildCartHelper } from "../../utils/guildCartHelper";

/**
 * Handle checkout quantity modal submission
 */
export async function handleCheckoutQuantityModal(
    interaction: ModalSubmitInteraction,
    listingId: string,
    paymentId: string,
    databaseUserId: string,
    discordUserId?: string
): Promise<void> {
    await interaction.deferUpdate();

    const quantityInput = interaction.fields.getTextInputValue('quantity');
    const quantity = parseInt(quantityInput);

    if (isNaN(quantity) || quantity <= 0) {
        await interaction.followUp({
            content: 'Please enter a valid positive number for quantity.',
            ephemeral: true
        });
        return;
    }

    const guildId = interaction.guild?.id;
    if (!guildId) {
        await interaction.followUp({
            content: 'This command can only be used in a server.',
            ephemeral: true
        });
        return;
    }

    try {
        // Fetch listing details
        const listingQuery = `
            SELECT l.*
            FROM asa.listings l
            WHERE l.listing_id = $1
        `;

        const listingResult = await pool.query(listingQuery, [listingId]);

        if (listingResult.rows.length === 0) {
            await interaction.followUp({
                content: 'This listing no longer exists.',
                ephemeral: true
            });
            return;
        }

        const listing = listingResult.rows[0];

        // Fetch payment details if paymentId is provided and not 'contact'
        let paymentMethod = 'Contact seller for details';
        let paymentIdNumber: number | undefined = undefined;

        if (paymentId !== 'contact') {
            try {
                const paymentIdAsNumber = parseInt(paymentId);

                const paymentQuery = `
                    SELECT p.*, i.name as item_name
                    FROM asa.payment_options p
                    LEFT JOIN asa.items i ON SPLIT_PART(p.item_id, ' ', 1) = SPLIT_PART(i.id, ' ', 1)
                    WHERE p.payment_id = $1
                `;

                const paymentResult = await pool.query(paymentQuery, [paymentIdAsNumber]);

                if (paymentResult.rows.length > 0) {
                    const paymentData = paymentResult.rows[0];
                    paymentIdNumber = paymentIdAsNumber;

                    if (paymentData.item_name) {
                        const formattedQuantity = NumberFormatter.formatWithCommas(paymentData.quantity);
                        paymentMethod = `${paymentData.item_name} x${formattedQuantity}`;
                    } else if (paymentData.custom_price) {
                        paymentMethod = paymentData.custom_price;
                    }
                }
            } catch (err) {
                console.error('Error parsing payment ID or querying database:', err);
            }
        }

        // Get user profile information for display
        const profileQuery = `
            SELECT username
            FROM public.profiles
            WHERE id = $1
        `;

        const profileResult = await pool.query(profileQuery, [databaseUserId]);
        const username = profileResult.rows.length > 0 ? profileResult.rows[0].username : 'Unknown User';

        // Get Discord user information
        let targetUser;
        if (discordUserId) {
            try {
                targetUser = await interaction.client.users.fetch(discordUserId);
            } catch (error) {
                console.log(`Could not fetch Discord user for ${discordUserId}`);
            }
        }

        if (!targetUser) {
            await interaction.followUp({
                content: 'Error: Could not find the Discord user for this checkout.',
                ephemeral: true
            });
            return;
        }

        // Add to cart for the Discord user (not the database user)
        cartManager.addItem(targetUser.id, guildId, {
            listingId: parseInt(listingId),
            title: listing.title || `Listing #${listingId}`,
            quantity: quantity,
            paymentMethod,
            paymentId: paymentIdNumber,
            listingType: listing.listing_type,
            category: listing.category || 'other'
        });

        // Get or create cart channel for the Discord user
        if (interaction.guild) {
            const { channel, isNew: isNewChannel } = await ChannelService.getOrCreateCartChannel(
                interaction.guild,
                interaction.client,
                targetUser.id,
                targetUser.username
            );

            if (!channel) {
                await interaction.followUp({
                    content: 'Error: Could not create or access cart channel.',
                    ephemeral: true
                });
                return;
            }

            if (isNewChannel) {
                // Get cart notify roles to tag
                const notifyRoles = await GuildRoleHelper.getGuildRoles(interaction.guild.id, 'cart_notify');

                // Create notification content
                let notificationContent = `🛒 **Cart Channel Created**\n${targetUser}, this is your personal cart channel! All your trades will be managed here.\n\n**Created by:** ${interaction.user}\n**For listings from:** **${username}**`;

                // Add role notifications if any are configured
                if (notifyRoles.length > 0) {
                    const roleMentions = notifyRoles.map(roleId => `<@&${roleId}>`).join(' ');
                    notificationContent += `\n\n🔔 **New Customer Alert:** ${roleMentions}`;
                }

                // Send initial message with user tag for identification
                await channel.send({
                    content: notificationContent
                });

                // Check if we need to create category threads
                const cartMode = await GuildCartHelper.getGuildCartMode(interaction.guild.id);
                if (cartMode === 'threads') {
                    const userMember = await interaction.guild.members.fetch(targetUser.id);
                    await CartThreadService.createCategoryThreads(channel, targetUser.id, userMember);
                }
            }

            // Determine where to post the item (main channel or appropriate thread)
            const category = listing.category || 'other';
            const userMember = await interaction.guild.members.fetch(targetUser.id);
            const targetChannel = await CartThreadService.getThreadForItem(
                channel,
                category,
                targetUser.id,
                userMember
            );

            // Add individual item message
            const itemData = CartSummaryService.createItemEmbed(
                listing.title || `Listing #${listingId}`,
                quantity,
                paymentMethod,
                targetUser.toString(),
                listing.listing_type
            );

            await targetChannel.send({
                embeds: [itemData.embed],
                components: itemData.components
            });

            // Update the cart summary at the end of the channel
            await CartSummaryService.updateCartSummary(
                channel,
                targetUser.id,
                guildId,
                targetUser.toString()
            );

            // Notify the admin who created the checkout
            const notificationMessage = isNewChannel
                ? `Item added to cart for ${targetUser.username}! Cart channel created in ${channel}.`
                : `Item added to ${targetUser.username}'s existing cart channel ${channel}.`;

            await interaction.followUp({
                content: notificationMessage,
                ephemeral: true
            });
        }

    } catch (error) {
        console.error('Error in checkout quantity modal handler:', error);
        await interaction.followUp({
            content: 'An error occurred while processing your request. Please try again later.',
            ephemeral: true
        });
    }
}
